# Vercel Deployment Guide for Grocero E-commerce

## Database Connection Setup

### 1. Environment Variables Setup

#### For Local Development:
1. Copy `.env.local.example` to `.env.local`
2. Update the `MONGODB_URI` with your actual MongoDB connection string
3. Set a secure `JWT_SECRET`

#### For Vercel Deployment:
1. Go to your Vercel project dashboard
2. Navigate to Settings → Environment Variables
3. Add the following variables:

```
MONGODB_URI = mongodb+srv://admin123:<EMAIL>/?retryWrites=true&w=majority&appName=Grocero
JWT_SECRET = your-super-secret-jwt-key-here
NODE_ENV = production
ENABLE_INSTRUMENTATION = true
```

### 2. Database Connection Features

The database connection has been optimized for Vercel serverless deployment:

- **Connection Caching**: Uses global connection caching to prevent multiple connections
- **Serverless Optimization**: Configured with appropriate timeouts and connection limits
- **Error Handling**: Robust error handling with retry mechanisms
- **Environment Detection**: Automatically detects production vs development environment

### 3. Testing Database Connection

After deployment, you can test the database connection by visiting:
```
https://your-app.vercel.app/api/test-db
```

This endpoint will show:
- Connection status
- Database name
- Available collections
- Connection details

### 4. Key Files Modified

1. **`src/app/lib/db/dbConnect.js`**: Enhanced with Vercel-optimized connection settings
2. **`src/app/lib/crud/BaseCrud.js`**: Updated to ensure connection on each operation
3. **`src/instrumentation.js`**: Improved error handling and environment detection
4. **`next.config.mjs`**: Added instrumentation hook and serverless configuration
5. **`vercel.json`**: Vercel-specific configuration for functions and environment

### 5. Deployment Steps

1. **Push to GitHub**: Ensure all changes are committed and pushed
2. **Connect to Vercel**: Link your GitHub repository to Vercel
3. **Set Environment Variables**: Add the required environment variables in Vercel dashboard
4. **Deploy**: Vercel will automatically deploy your application
5. **Test**: Visit `/api/test-db` to verify database connection

### 6. Troubleshooting

#### Common Issues:

1. **Connection Timeout**: 
   - Check if MongoDB Atlas allows connections from all IPs (0.0.0.0/0)
   - Verify the connection string is correct

2. **Environment Variables Not Found**:
   - Ensure variables are set in Vercel dashboard
   - Redeploy after adding environment variables

3. **Models Not Initialized**:
   - Check the instrumentation.js logs in Vercel function logs
   - Ensure ENABLE_INSTRUMENTATION=true in production

#### Debugging:
- Check Vercel function logs for detailed error messages
- Use the `/api/test-db` endpoint to diagnose connection issues
- Monitor MongoDB Atlas connection logs

### 7. Performance Considerations

- Connection pooling is configured for optimal serverless performance
- Database operations include automatic connection establishment
- Error handling prevents application crashes from database issues

### 8. Security Notes

- Never commit `.env.local` to version control
- Use strong, unique JWT secrets
- Regularly rotate database credentials
- Monitor database access logs
