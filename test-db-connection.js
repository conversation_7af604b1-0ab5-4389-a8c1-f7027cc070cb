#!/usr/bin/env node

/**
 * Database Connection Test Script
 * Run this script to test your MongoDB connection before deployment
 * 
 * Usage: node test-db-connection.js
 */

import mongoose from 'mongoose';
import dbConnect from './src/app/lib/db/dbConnect.js';

async function testConnection() {
    console.log('🔍 Testing MongoDB connection...\n');
    
    try {
        // Test the connection
        console.log('⏳ Connecting to database...');
        await dbConnect();
        
        // Get connection info
        const connection = mongoose.connection;
        console.log('✅ Connection successful!');
        console.log(`📊 Database: ${connection.db.databaseName}`);
        console.log(`🌐 Host: ${connection.host}:${connection.port}`);
        console.log(`🔗 Connection State: ${getConnectionState(connection.readyState)}`);
        
        // List collections
        console.log('\n📋 Available Collections:');
        const collections = await connection.db.listCollections().toArray();
        if (collections.length === 0) {
            console.log('   No collections found (database might be empty)');
        } else {
            collections.forEach(col => {
                console.log(`   - ${col.name}`);
            });
        }
        
        // Test a simple query if collections exist
        if (collections.length > 0) {
            console.log('\n🧪 Testing basic query...');
            const testCollection = collections[0].name;
            const count = await connection.db.collection(testCollection).countDocuments();
            console.log(`   Documents in ${testCollection}: ${count}`);
        }
        
        console.log('\n✅ Database connection test completed successfully!');
        
    } catch (error) {
        console.error('❌ Database connection failed:');
        console.error(`   Error: ${error.message}`);
        
        if (error.message.includes('ENOTFOUND')) {
            console.error('   💡 This usually means the hostname is incorrect or DNS resolution failed');
        } else if (error.message.includes('authentication failed')) {
            console.error('   💡 Check your username and password in the connection string');
        } else if (error.message.includes('timeout')) {
            console.error('   💡 Check your network connection and MongoDB Atlas IP whitelist');
        }
        
        process.exit(1);
    } finally {
        // Close the connection
        if (mongoose.connection.readyState === 1) {
            await mongoose.connection.close();
            console.log('🔌 Connection closed');
        }
    }
}

function getConnectionState(state) {
    const states = {
        0: 'disconnected',
        1: 'connected',
        2: 'connecting',
        3: 'disconnecting'
    };
    return states[state] || 'unknown';
}

// Run the test
testConnection().catch(console.error);
