'use client';

import React, { useEffect, useState, useRef } from 'react'
import { INITIAL_PRODUCT } from '../page';
import { useRouter } from 'next/navigation';
import { FaCloudUploadAlt } from 'react-icons/fa';
import { Label } from '@/components/ui/label';
import { Classes } from "@/components/ui/input";
import { Button } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Tabs,
	TabsContent,
	TabsList,
	TabsTrigger,
} from "@/components/ui/tabs";
import PreviewCard from './PreviewCard';
import { useToast } from '@/hooks/use-toast';
import { ToastAction } from "@/components/ui/toast";

export default function AddSlide() {
	const router = useRouter();
	const { toast } = useToast();
	const [newSlide, setNewSlide] = useState(INITIAL_PRODUCT);
	const [imageFile, setImageFile] = useState(null);
	const [imagePreview, setImagePreview] = useState(null);
	const [error, setError] = useState('');
	const [loading, setLoading] = useState(false);

	const handleImageUpload = (file) => {
		if (!file) return;
		if (!file.type.startsWith('image/')) {
			setError('Please upload an image file');
			return;
		}

		setError('');
		setImageFile(file);
		const reader = new FileReader();
		reader.onloadend = () => setImagePreview(reader.result);
		reader.readAsDataURL(file);
	};

	const resetForm = () => {
		setNewSlide(INITIAL_PRODUCT);
		setImageFile(null);
		setImagePreview(null);
		setError('');
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setLoading(true);
		setError('');

		try {
			if (!newSlide.slide_name) throw new Error('Please fill in all required fields');
			if (!imageFile) throw new Error('Please select an image');

			const base64Data = await new Promise((resolve) => {
				const reader = new FileReader();
				reader.onloadend = () => resolve(reader.result);
				reader.readAsDataURL(imageFile);
			});

			const response = await fetch('/api/admin/slides', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					slide_name: newSlide.slide_name,
					type: newSlide.type,
					image: {
						name: imageFile.name,
						contentType: imageFile.type,
						base64Data,
						description: ''
					},
				}),
			});

			const data = await response.json();
			if (data.returncode === 200) {
				toast({
					title: 'Success',
					description: data.message,
					action: <ToastAction altText="Okay"> Okay </ToastAction>,
				});
			}
			else {
				toast({
					title: 'Error',
					description: data.message,
					action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
				});
			}

			resetForm();
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Temporary Server Error, Please try again later...',
				action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
			});
		} finally {
			setLoading(false);
			router.refresh();
		}
	};


	return (
		<Tabs>
			<TabsList className="grid w-full grid-cols-2">
				<TabsTrigger value="form">Add New</TabsTrigger>
				<TabsTrigger value="preview">Preview</TabsTrigger>
			</TabsList>
			<TabsContent value="form">
				<Card>
					<CardHeader>
						<CardTitle>Add New Slide</CardTitle>
						<CardDescription>
							Slide Entry Form for home page.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-2">
						<form onSubmit={handleSubmit} className="space-y-6">
							<div>
								<Label htmlFor="SlideName" className="text-lg">
									Slide Name
									<span className='text-red-500'> * </span>
								</Label>
								<input
									type="text"
									value={newSlide.slide_name}
									onChange={(e) => setNewSlide({ ...newSlide, slide_name: e.target.value })}
									className={`${Classes} mt-2 backdrop-blur-sm`}
									placeholder='eg; Slide 1'
									required
								/>
							</div>

							<div>
								<Label htmlFor="Type" className="text-lg">
									Type
								</Label>
								<textarea
									value={newSlide.type}
									onChange={(e) => setNewSlide({ ...newSlide, type: e.target.value })}
									rows={3}
									className={`${Classes} mt-2 backdrop-blur-sm`}
									placeholder='(Optional)'
								/>
							</div>

							<div>
								<Label htmlFor="categoryImage" className="text-lg">Category Image</Label>
								<ImageUploader onImageUpload={handleImageUpload} imagePreview={imagePreview} />
								{error && <p className="mt-2 text-sm text-red-500">{error}</p>}
							</div>

							<Button
								type="submit"
								disabled={loading}
								className="w-full py-6 text-lg  text-white bg-primary hover:from-primary-600 hover:to-primary transition-all duration-300"
							>
								{loading ? 'Adding...' : 'Add Slide'}
							</Button>
						</form>
					</CardContent>
				</Card>
			</TabsContent>
			<TabsContent value="preview">
				<Card>
					<CardHeader>
						<CardTitle>Slide Display</CardTitle>
						<CardDescription>
							If it looks right, its gonna be good.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-2">
						<PreviewCard previewImage={imagePreview} />
					</CardContent>
				</Card>
			</TabsContent>
		</Tabs>
	)
}

// <---- UI Components ---->

// Custom hook for drag and drop
const useDragAndDrop = (onFileDrop) => {
	const [isDragging, setIsDragging] = useState(false);
	const dropZoneRef = useRef(null);

	const handleDrag = (e) => {
		e.preventDefault();
		e.stopPropagation();
	};

	const handleDragIn = (e) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(true);
	};

	const handleDragOut = (e) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(false);
	};

	const handleDrop = (e) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(false);

		const files = e.dataTransfer.files;
		if (files?.length > 0) {
			onFileDrop(files[0]);
		}
	};

	useEffect(() => {
		const dropZone = dropZoneRef.current;
		if (!dropZone) return;

		dropZone.addEventListener('dragenter', handleDragIn);
		dropZone.addEventListener('dragleave', handleDragOut);
		dropZone.addEventListener('dragover', handleDrag);
		dropZone.addEventListener('drop', handleDrop);

		return () => {
			dropZone.removeEventListener('dragenter', handleDragIn);
			dropZone.removeEventListener('dragleave', handleDragOut);
			dropZone.removeEventListener('dragover', handleDrag);
			dropZone.removeEventListener('drop', handleDrop);
		};
	}, [onFileDrop]);

	return { isDragging, dropZoneRef };
};

// ImageUploader Component
const ImageUploader = ({ onImageUpload, imagePreview }) => {
	const fileInputRef = useRef(null);
	const { isDragging, dropZoneRef } = useDragAndDrop(onImageUpload);

	return (
		<div
			ref={dropZoneRef}
			onClick={() => fileInputRef.current?.click()}
			className={`
        mt-2 p-8 border-2 border-dashed rounded-2xl cursor-pointer
        backdrop-blur-sm backdrop-filter
        transition-all duration-300 ease-in-out
        ${isDragging
					? 'border-primary bg-primary/10 scale-102'
					: 'border-gray-300 hover:border-primary hover:shadow-lg dark:border-gray-600'
				}
        ${imagePreview ? 'pb-4' : 'flex flex-col items-center justify-center min-h-[240px]'}
        group
      `}
		>
			<input
				type="file"
				ref={fileInputRef}
				className="hidden"
				accept="image/*"
				onChange={(e) => onImageUpload(e.target.files?.[0])}
			/>

			{imagePreview ? (
				<div className="space-y-4 relative group">
					<div className="overflow-hidden rounded-xl">
						<img
							src={imagePreview}
							alt="Preview"
							className="mx-auto max-h-[240px] object-contain transform transition-transform group-hover:scale-105"
						/>
					</div>
					<p className="text-sm text-center text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity">
						Click or drag to replace image
					</p>
				</div>
			) : (
				<div className="transform transition-transform group-hover:scale-110">
					<FaCloudUploadAlt className="w-16 h-16 text-gray-400 mb-6 group-hover:text-primary transition-quantity_types" />
					<p className="text-lg text-gray-600 text-center mb-2 dark:text-gray-300">
						Drag and drop your image here
					</p>
					<p className="text-sm text-gray-500 text-center dark:text-gray-400">
						or click to select a file
					</p>
				</div>
			)}
		</div>
	);
};
