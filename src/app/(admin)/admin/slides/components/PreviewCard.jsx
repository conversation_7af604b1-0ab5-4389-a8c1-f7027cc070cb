import React from 'react';
import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious,
} from "@/components/ui/carousel";
import Image from 'next/image'; // Renamed to avoid conflict

function PreviewCard({ previewImage }) { // Renamed from Image to previewImage
	return (
		<Carousel>
			<CarouselContent>
				{/* Preview image */}
				<CarouselItem>
					<Image
						src={previewImage || '/login_bg.jpg'}
						alt='Preview'
						width={2500}
						height={2500}
						className='w-full h-[200px] md:h-[500px] object-cover rounded-2xl'
					/>
				</CarouselItem>
			</CarouselContent>
			<CarouselPrevious />
			<CarouselNext />
		</Carousel>
	);
}

export default PreviewCard;
