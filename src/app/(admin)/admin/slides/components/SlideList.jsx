'use client';

import React, { useEffect, useState } from 'react'
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import Image from 'next/image';
import { TrashIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { ToastAction } from "@/components/ui/toast";

function SlideList() {

	const { toast } = useToast();
	const [Slides, setSlides] = useState([]);

	useEffect(() => {
		fetchSlides();
	}, []);

	const fetchSlides = async () => {
		try {
			const response = await fetch('/api/admin/slides');
			const data = await response.json();
			if (response.ok && data.output.length > 0) {
				setSlides(data.output);
			}
		} catch (error) {
			console.log('Error fetching Slides:', error);
		}
	};

	const handleDelete = async (id) => {
		if (!confirm('Are you sure you want to delete this slide?')) return;

		try {
			const response = await fetch(`/api/admin/slides?id=${id}`, {
				method: 'DELETE',
			});

			const data = await response.json();
			console.log(data);
			if (data.returncode === 200) {
				toast({
					title: 'Success',
					description: data.message,
					action: <ToastAction altText="Okay"> Okay </ToastAction>,
				});
			}
			else {
				toast({
					title: 'Error',
					description: data.message,
					action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
				});
			}
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Temporary Server Error, Please try again later...',
				action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
			});
		} finally {
			await fetchSlides();
		}
	};


	return (
		<>
			<h1 className='my-6 text-3xl text-primary'>Slides List</h1>
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>SR No.</TableHead>
						<TableHead>Image</TableHead>
						<TableHead>Slide Name</TableHead>
						<TableHead>Type</TableHead>
						<TableHead>Created By</TableHead>
						<TableHead>Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{
						Slides.map((slide, index) => (
							<TableRow key={index}>
								<TableCell>{index + 1}</TableCell>
								<TableCell className='w-[400px] h-[200px]'>
									<Image
										src={slide.Image.url}
										alt={slide.Name}
										width={1000}
										height={1000}
										className='w-full h-full object-contain rounded-2xl'
									/>
								</TableCell>
								<TableCell>
									{slide.Name}
								</TableCell>
								<TableCell>
									{slide.Type}
								</TableCell>
								<TableCell>
									{slide.createdBy.FirstName} {slide.createdBy.LastName}
								</TableCell>
								<TableCell>
									<div className='flex items-end gap-4'>
										<TrashIcon onClick={() => { handleDelete(slide._id) }} className='bg-red-100 p-1 text-red-500 w-[30px] h-[30px] cursor-pointer rounded-lg' />
									</div>
								</TableCell>
							</TableRow>
						))
					}
				</TableBody>
			</Table>
		</>
	)
}

export default SlideList
