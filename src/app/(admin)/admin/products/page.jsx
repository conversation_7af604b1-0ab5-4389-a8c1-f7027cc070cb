'use client';

import AddProduct from './components/AddProduct';
import ProductList from './components/ProductList';


// Constants
export const INITIAL_PRODUCT = {
	product_name: '',
	description: '',
	quantity_type: '',
	category_id: '',
	category_name: '', // For Verification
	mrp: 0,
	selling_price: null,
	available_quantity: 0,
	limit: null,
	Image: []
};


export default function ProductPage() {


	return (
		<section className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex flex-col items-center">
			<div className="backdrop-blur-lg backdrop-filter w-full pb-4">
				<h1 className="text-4xl font-bold p-6 text-center text-white bg-primary">
					Manage Products
				</h1>
				<div className='p-10'>
					<AddProduct />
					<ProductList />
				</div>
			</div>

		</section>
	);
}
