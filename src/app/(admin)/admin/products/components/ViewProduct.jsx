'use client';

import Image from 'next/image'
import React, { useState, useEffect } from 'react'

function ViewProduct({ product }) {
	const [Color, setColor] = useState();

	useEffect(() => {
		if (product.Status === 'Out of Stock') {
			setColor('red');
		} else if (product.Status === 'Limited Stock') {
			setColor('yellow');
		}
	}, [])


	return (
		<div className='grid grid-cols-1 md:grid-cols-2 gap-4 p-7 bg-white text-black'>
			<Image
				src={product?.Images[0]?.url || '/placeholder.png'}
				alt={product.Name}
				width={300}
				height={300}
				className='p-5 h-[200px] w-[200px] object-contain rounded-lg border'
			/>
			<div className='flex flex-col gap-3'>
				{
					Color &&
					(
						<div className='lg:w-1/5'>
							<div className={`text-${Color}-800 border border-${Color}-800 px-3 py-1 rounded-2xl text-xs w-auto text-center`}>
								{product.Status}
							</div>
						</div>
					)
				}
				<h1 className='text-2xl font-semibold'>{product.Name || 'Product Name'}</h1>
				<p className='text-sm text-gray-500'>{product.Description || 'Description'}</p>
				<div className='flex gap-3 flex-wrap py-2'>
					<div className='text-teal-800 border border-teal-800 px-3 py-1 rounded-2xl text-xs'>
						{product.Categories.Name || 'Category Name'}
					</div>
				</div>
				<h2 className='text-3xl font-semibold flex gap-3'>
					{
						product?.SellingPrice && (

							<p>Rs. {product?.SellingPrice}</p>
						)
					}
					<p className={`${product?.SellingPrice && "line-through text-gray-700 opacity-50"}`}>
						Rs. {product.MarketRatePrice}
					</p>
				</h2>
				<h2 className='text-sm'> Quantity ({product.QuantityType || '100g'}) </h2>

			</div>
		</div>
	)
}

export default ViewProduct
