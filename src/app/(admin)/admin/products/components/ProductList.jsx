'use client';

import React, { useEffect, useState } from 'react'
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	<PERSON><PERSON>Title,
	DialogTrigger,
} from "@/components/ui/dialog";
import Image from 'next/image';
import { Eye, Pencil, TrashIcon } from 'lucide-react';
import EditProduct from './EditProduct';
import ViewProduct from './ViewProduct';
import { useToast } from '@/hooks/use-toast';
import { ToastAction } from "@/components/ui/toast";

function ProductList() {

	const { toast } = useToast();
	const [Products, setProducts] = useState([]);

	useEffect(() => {
		fetchProducts();
	}, []);

	const fetchProducts = async () => {
		try {
			const response = await fetch('/api/admin/products');
			const data = await response.json();
			if (response.ok && data.output.length > 0) {
				setProducts(data.output);
			}
		} catch (error) {
			console.log('Error fetching Products:', error);
		}
	};

	const handleDelete = async (id) => {
		if (!confirm('Are you sure you want to delete this product?')) return;

		try {
			const response = await fetch(`/api/admin/products?id=${id}`, {
				method: 'DELETE',
			});

			const data = await response.json();
			console.log(data);
			if (data.returncode === 200) {
				toast({
					title: 'Success',
					description: data.message,
					action: <ToastAction altText="Okay"> Okay </ToastAction>,
				});
			}
			else {
				toast({
					title: 'Error',
					description: data.message,
					action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
				});
			}
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Temporary Server Error, Please try again later...',
				action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
			});
		} finally {
			await fetchProducts();
		}
	};


	return (
		<>
			<h1 className='my-6 text-3xl text-primary'>Products List</h1>
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>SR No.</TableHead>
						<TableHead>Image</TableHead>
						<TableHead>Name</TableHead>
						<TableHead className='w-[100px] flex flex-wrap items-center'>
							Description
						</TableHead>
						<TableHead>MRP</TableHead>
						<TableHead>Selling Price</TableHead>
						<TableHead>Each Quantity</TableHead>
						<TableHead>Quantity</TableHead>
						<TableHead>Created By</TableHead>
						<TableHead>Status</TableHead>
						<TableHead>Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{
						Products.map((product, index) => (
							<TableRow key={index}>
								<TableCell>{index + 1}</TableCell>
								<TableCell className='w-[100px] h-[100px]'>
									<Image
										src={product.Images[0].url}
										alt={product.Name}
										width={1000}
										height={1000}
										className='w-full h-full object-contain'
									/>
								</TableCell>
								<TableCell>
									{product.Name}
								</TableCell>
								<TableCell>
									{product.Description}
								</TableCell>
								<TableCell>
									{product.MarketRatePrice}
								</TableCell>
								<TableCell>
									{product.SellingPrice}
								</TableCell>
								<TableCell>
									{product.QuantityType}
								</TableCell>
								<TableCell>
									{product.AvailableQuantity}
								</TableCell>
								<TableCell>
									{product.createdBy.FirstName} {product.createdBy.LastName}
								</TableCell>
								<TableCell>
									<p
										className={`p-1 text-center rounded-lg
												${product.Status === 'Low Stock'
												? 'text-yellow-500 bg-yellow-100'
												: product.Status === 'Out of Stock'
													? 'text-red-500 bg-red-100'
													: product.Status === 'In Stock'
														? 'text-green-500 bg-green-100'
														: 'text-gray-500 bg-gray-100'
											}`}
									>
										{product.Status}
									</p>
								</TableCell>
								<TableCell>
									<div className='flex items-end gap-4'>
										<Dialog>
											<DialogTrigger asChild>
												<Eye className='bg-blue-100 p-1 text-blue-500 w-[30px] h-[30px] cursor-pointer rounded-lg' />
											</DialogTrigger>
											<DialogContent>
												<DialogHeader>
													<DialogTitle>Product Info</DialogTitle>
												</DialogHeader>
												<div>
													<ViewProduct product={product} />
												</div>
											</DialogContent>
										</Dialog>
										<Dialog>
											<DialogTrigger asChild>
												<Pencil className='bg-yellow-100 p-1 text-yellow-500 w-[30px] h-[30px] cursor-pointer rounded-lg' />
											</DialogTrigger>
											<DialogContent>
												<DialogHeader>
													<DialogTitle>Update Product Info</DialogTitle>
												</DialogHeader>
												<div>
													<EditProduct product={product} />
												</div>
											</DialogContent>
										</Dialog>
										<TrashIcon onClick={() => { handleDelete(product._id) }} className='bg-red-100 p-1 text-red-500 w-[30px] h-[30px] cursor-pointer rounded-lg' />
									</div>
								</TableCell>
							</TableRow>
						))
					}
				</TableBody>
			</Table>
		</>
	)
}

export default ProductList
