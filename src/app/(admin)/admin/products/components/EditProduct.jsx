'use client';

import React, { useEffect, useState } from 'react'
import { Label } from '@/components/ui/label';
import { Classes } from "@/components/ui/input";
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { IndianRupeeIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { ToastAction } from "@/components/ui/toast";

function EditProduct({ product }) {
	const router = useRouter();
	const { toast } = useToast();
	const [existingProduct, setExistingProduct] = useState({
		product_id: '',
		product_name: '',
		description: '',
		mrp: 0,
		selling_price: 0,
		quantity_type: '',
		available_quantity: 0,
		limit: 0,
	});

	useEffect(() => {
		setExistingProduct({
			product_id: product._id,
			product_name: product.Name,
			description: product.Description,
			mrp: product.MarketRatePrice,
			selling_price: product.SellingPrice,
			quantity_type: product.QuantityType,
			available_quantity: product.AvailableQuantity,
			limit: product.StockLimitAlert,
		})
	}, []);

	const handleSubmit = async (e) => {
		e.preventDefault();

		try {
			if (!existingProduct.product_name) throw new Error('Please fill in all required fields');

			const response = await fetch('/api/admin/products', {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					product_id: existingProduct.product_id,
					product_name: existingProduct.product_name,
					description: existingProduct.description,
					mrp: existingProduct.mrp,
					selling_price: existingProduct.selling_price,
					quantity_type: existingProduct.quantity_type,
					available_quantity: existingProduct.available_quantity,
					limit: existingProduct.limit,
				}),
			});

			const data = await response.json();
			if (data.returncode === 200) {
				toast({
					title: 'Success',
					description: data.message,
					action: <ToastAction altText="Okay"> Okay </ToastAction>,
				});
			}
			else {
				toast({
					title: 'Error',
					description: data.message,
					action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
				});
			}
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Temporary Server Error, Please try again later...',
				action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
			});
		} finally {
			router.refresh();
		}
	};


	return (
		<>
			<form onSubmit={handleSubmit} className="space-y-6">
				<div>
					<Label htmlFor="ProductName" className="text-lg">
						Product Name
						<span className='text-red-500'> * </span>
					</Label>
					<input
						type="text"
						value={existingProduct.product_name}
						onChange={(e) => setExistingProduct({ ...existingProduct, product_name: e.target.value })}
						className={`${Classes} mt-2 backdrop-blur-sm`}
						placeholder='eg; Cauliflower (500g)'
						required
					/>
				</div>

				<div>
					<Label htmlFor="Desc" className="text-lg">Product Description</Label>
					<textarea
						value={existingProduct.description}
						onChange={(e) => setExistingProduct({ ...existingProduct, description: e.target.value })}
						rows={3}
						className={`${Classes} mt-2 backdrop-blur-sm`}
						placeholder='(Optional)'
					/>
				</div>

				<div>
					<Label htmlFor="mrp" className="text-lg">
						Market Rate Price (MRP)
						<span className='text-red-500'> * </span>

					</Label>
					<div className='flex gap-2 items-center'>
						<IndianRupeeIcon className='w-4 h-4 text-gray-500' />
						<input
							type="number"
							value={existingProduct.mrp}
							onChange={(e) => setExistingProduct({ ...existingProduct, mrp: e.target.value })}
							className={`${Classes} mt-2 backdrop-blur-sm`}
							placeholder='eg; 30'
							required
						/>
					</div>
				</div>

				<div>
					<Label htmlFor="selling_price" className="text-lg">
						Selling Price (Discount)
					</Label>
					<div className='flex gap-2 items-center'>
						<IndianRupeeIcon className='w-4 h-4 text-gray-500' />
						<input
							type="number"
							value={existingProduct.selling_price || ''}
							onChange={(e) => setExistingProduct({ ...existingProduct, selling_price: e.target.value })}
							className={`${Classes} mt-2 backdrop-blur-sm`}
							placeholder='eg; 30'
						/>
					</div>
				</div>

				<div>
					<Label htmlFor="quantity_type" className="text-lg">
						Quantity Type
						<span className='text-red-500'> * </span>

					</Label>
					<input
						type="text"
						value={existingProduct.quantity_type}
						onChange={(e) => setExistingProduct({ ...existingProduct, quantity_type: e.target.value })}
						className={`${Classes} mt-2 backdrop-blur-sm`}
						placeholder='eg; 500g'
						required
					/>
				</div>


				<div>
					<Label htmlFor="available stock" className="text-lg">
						Available Quantity
						<span className='text-red-500'> * </span>

					</Label>
					<input
						type="number"
						value={existingProduct.available_quantity}
						onChange={(e) => setExistingProduct({ ...existingProduct, available_quantity: e.target.value })}
						className={`${Classes} mt-2 backdrop-blur-sm`}
						placeholder='eg; 30'
						required
					/>
				</div>

				<div>
					<Label htmlFor="selling_price" className="text-lg">
						Limit Rate Alert
					</Label>
					<p className='text-xs italic text-gray-500'>*It will set the status of Product as Out of Stock, Low Stock, etc. </p>
					<input
						type="number"
						value={existingProduct.limit || ''}
						onChange={(e) => setExistingProduct({ ...existingProduct, limit: e.target.value })}
						className={`${Classes} mt-2 backdrop-blur-sm`}
						placeholder='eg; 20'
					/>
				</div>


				<Button
					type="submit"
					className="w-full py-6 text-lg  text-white bg-primary hover:from-primary-600 hover:to-primary transition-all duration-300"
				>
					Update Product
				</Button>
			</form>
		</>
	)
}

export default EditProduct
