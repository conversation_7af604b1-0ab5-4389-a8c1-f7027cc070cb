'use client';

import Image from 'next/image'
import React, { useState, useEffect } from 'react'

function PreviewCard({ product, image }) {
	const [Status, setStatus] = useState('');
	const [Color, setColor] = useState();

	useEffect(() => {
		if (product.available_quantity === 0) {
			setStatus('Out of Stock');
			setColor('red');
		} else if (product.available_quantity <= (product?.limit || 10)) {
			setStatus('Limited Stock');
			setColor('yellow');
		}
	}, [])


	return (
		<div className='grid grid-cols-1 md:grid-cols-2 gap-4 p-7 bg-white text-black'>
			<Image
				src={image || '/placeholder.png'}
				alt={product.product_name}
				width={300}
				height={300}
				className='p-5 h-[200px] w-[180px] md:h-[320px] md:w-[300px] object-contain rounded-lg border'
			/>
			<div className='flex flex-col gap-3'>
				{
					Color &&
					(
						<div className='lg:w-1/5'>
							<div className={`text-${Color}-800 border border-${Color}-800 px-3 py-1 rounded-2xl text-xs w-auto text-center`}>
								{Status}
							</div>
						</div>
					)
				}
				<h1 className='text-2xl font-semibold'>{product.product_name || 'Product Name'}</h1>
				<p className='text-sm text-gray-500'>{product.description || 'Description'}</p>
				<div className='flex gap-3 flex-wrap py-2'>
					<div className='text-teal-800 border border-teal-800 px-3 py-1 rounded-2xl text-xs'>
						{product.category_name || 'Category Name'}
					</div>
				</div>
				<h2 className='text-3xl font-semibold flex gap-3'>
					{
						product?.selling_price && (

							<p>Rs. {product?.selling_price}</p>
						)
					}
					<p className={`${product?.selling_price && "line-through text-teal-700 opacity-50"}`}>
						Rs. {product.mrp}
					</p>
				</h2>
				<h2 className='text-sm'> Quantity ({product.quantity_type || '100g'}) </h2>

			</div>
		</div>
	)
}

export default PreviewCard
