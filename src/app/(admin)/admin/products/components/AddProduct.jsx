'use client';

import React, { useEffect, useState, useRef } from 'react'
import { INITIAL_PRODUCT } from '../page';
import { useRouter } from 'next/navigation';
import { FaCloudUploadAlt } from 'react-icons/fa';
import { Label } from '@/components/ui/label';
import { Classes } from "@/components/ui/input";
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Tabs,
	TabsContent,
	TabsList,
	TabsTrigger,
} from "@/components/ui/tabs";
import Image from 'next/image';
import { ChevronDown, IndianRupeeIcon } from 'lucide-react';
import PreviewCard from './PreviewCard';
import { useToast } from '@/hooks/use-toast';
import { ToastAction } from "@/components/ui/toast";

export default function AddProduct() {
	const router = useRouter();
	const { toast } = useToast();
	const [Category, setCategory] = useState([]);
	const [newProduct, setNewProduct] = useState(INITIAL_PRODUCT);
	const [imageFile, setImageFile] = useState(null);
	const [imagePreview, setImagePreview] = useState(null);
	const [error, setError] = useState('');
	const [loading, setLoading] = useState(false);

	useEffect(() => {
		fetchCategories();
	}, []);

	const fetchCategories = async () => {
		setLoading(true);
		try {
			const category_response = await fetch('/api/admin/product_categories');
			const category_data = await category_response.json();
			if (category_response.ok) {
				setCategory(category_data.output)
			}
		} catch (error) {
			console.error('Error fetching Products:', error);
		} finally {
			setLoading(false);
		}
	};

	const handleImageUpload = (file) => {
		if (!file) return;
		if (!file.type.startsWith('image/')) {
			setError('Please upload an image file');
			return;
		}

		setError('');
		setImageFile(file);
		const reader = new FileReader();
		reader.onloadend = () => setImagePreview(reader.result);
		reader.readAsDataURL(file);
	};

	const resetForm = () => {
		setNewProduct(INITIAL_PRODUCT);
		setImageFile(null);
		setImagePreview(null);
		setError('');
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setLoading(true);
		setError('');

		try {
			if (!newProduct.product_name) throw new Error('Please fill in all required fields');
			if (!imageFile) throw new Error('Please select an image');

			const base64Data = await new Promise((resolve) => {
				const reader = new FileReader();
				reader.onloadend = () => resolve(reader.result);
				reader.readAsDataURL(imageFile);
			});

			const response = await fetch('/api/admin/products', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					product_name: newProduct.product_name,
					description: newProduct.description,
					category_id: newProduct.category_id,
					mrp: newProduct.mrp,
					selling_price: newProduct.selling_price,
					quantity_type: newProduct.quantity_type,
					available_quantity: newProduct.available_quantity,
					limit: newProduct.limit,
					image: {
						name: imageFile.name,
						contentType: imageFile.type,
						base64Data,
						description: ''
					},
				}),
			});

			const data = await response.json();
			if (data.returncode === 200) {
				toast({
					title: 'Success',
					description: data.message,
					action: <ToastAction altText="Okay"> Okay </ToastAction>,
				});
			}
			else {
				toast({
					title: 'Error',
					description: data.message,
					action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
				});
			}

			resetForm();
			await fetchCategories();
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Temporary Server Error, Please try again later...',
				action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
			});
		} finally {
			setLoading(false);
			router.refresh();
		}
	};


	return (
		<Tabs>
			<TabsList className="grid w-full grid-cols-2">
				<TabsTrigger value="form">Add New</TabsTrigger>
				<TabsTrigger value="preview">Preview</TabsTrigger>
			</TabsList>
			<TabsContent value="form">
				<Card>
					<CardHeader>
						<CardTitle>Add New Product</CardTitle>
						<CardDescription>
							Product Entry Form for your stock.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-2">
						<form onSubmit={handleSubmit} className="space-y-6">
							<div>
								<Label htmlFor="ProductName" className="text-lg">
									Product Name
									<span className='text-red-500'> * </span>
								</Label>
								<input
									type="text"
									value={newProduct.product_name}
									onChange={(e) => setNewProduct({ ...newProduct, product_name: e.target.value })}
									className={`${Classes} mt-2 backdrop-blur-sm`}
									placeholder='eg; Cauliflower (500g)'
									required
								/>
							</div>

							<div>
								<Label htmlFor="Desc" className="text-lg">Product Description</Label>
								<textarea
									value={newProduct.description}
									onChange={(e) => setNewProduct({ ...newProduct, description: e.target.value })}
									rows={3}
									className={`${Classes} mt-2 backdrop-blur-sm`}
									placeholder='(Optional)'
								/>
							</div>

							<div>
								<Label htmlFor="category" className="text-lg">
									Product Category
									<span className='text-red-500'> * </span>

								</Label>
								<DropdownMenu>
									<DropdownMenuTrigger asChild>
										<h2 className='flex justify-between items-center p-2 px-10 bg-slate-200 border rounded-full cursor-pointer'>
											{newProduct.category_name || (<><p>Choose</p> <ChevronDown /></>)}
										</h2>
									</DropdownMenuTrigger>
									<DropdownMenuContent>
										<DropdownMenuLabel>Choose One </DropdownMenuLabel>
										<DropdownMenuSeparator />
										{
											Category.map((category, index) => (
												<div
													key={index}
													onClick={() => setNewProduct({
														...newProduct,
														category_id: category._id,
														category_name: category.Name
													})}
												>
													<DropdownMenuItem className="flex gap-4 items-center cursor-pointer">
														<Image
															src={category.Icon[0].url}
															width={40}
															height={40}
															alt={category.Name}
														/>
														<h2 className='text-lg'> {category.Name} </h2>
													</DropdownMenuItem>
												</div>
											))
										}
									</DropdownMenuContent>
								</DropdownMenu>
							</div>

							<div>
								<Label htmlFor="mrp" className="text-lg">
									Market Rate Price (MRP)
									<span className='text-red-500'> * </span>

								</Label>
								<div className='flex gap-2 items-center'>
									<IndianRupeeIcon className='w-4 h-4 text-gray-500' />
									<input
										type="number"
										value={newProduct.mrp}
										onChange={(e) => setNewProduct({ ...newProduct, mrp: e.target.value })}
										className={`${Classes} mt-2 backdrop-blur-sm`}
										placeholder='eg; 30'
										required
									/>
								</div>
							</div>

							<div>
								<Label htmlFor="selling_price" className="text-lg">
									Selling Price (Discount)
								</Label>
								<div className='flex gap-2 items-center'>
									<IndianRupeeIcon className='w-4 h-4 text-gray-500' />
									<input
										type="number"
										value={newProduct.selling_price || ''}
										onChange={(e) => setNewProduct({ ...newProduct, selling_price: e.target.value })}
										className={`${Classes} mt-2 backdrop-blur-sm`}
										placeholder='eg; 30'
									/>
								</div>
							</div>

							<div>
								<Label htmlFor="quantity_type" className="text-lg">
									Quantity Type
									<span className='text-red-500'> * </span>

								</Label>
								<input
									type="text"
									value={newProduct.quantity_type}
									onChange={(e) => setNewProduct({ ...newProduct, quantity_type: e.target.value })}
									className={`${Classes} mt-2 backdrop-blur-sm`}
									placeholder='eg; 500g'
									required
								/>
							</div>


							<div>
								<Label htmlFor="available stock" className="text-lg">
									Available Quantity
									<span className='text-red-500'> * </span>

								</Label>
								<input
									type="number"
									value={newProduct.available_quantity}
									onChange={(e) => setNewProduct({ ...newProduct, available_quantity: e.target.value })}
									className={`${Classes} mt-2 backdrop-blur-sm`}
									placeholder='eg; 30'
									required
								/>
							</div>

							<div>
								<Label htmlFor="selling_price" className="text-lg">
									Limit Rate Alert
								</Label>
								<p className='text-xs italic text-gray-500'>*It will set the status of Product as Out of Stock, Low Stock, etc. </p>
								<input
									type="number"
									value={newProduct.limit || ''}
									onChange={(e) => setNewProduct({ ...newProduct, limit: e.target.value })}
									className={`${Classes} mt-2 backdrop-blur-sm`}
									placeholder='eg; 20'
								/>
							</div>



							<div>
								<Label htmlFor="categoryImage" className="text-lg">Category Image</Label>
								<ImageUploader onImageUpload={handleImageUpload} imagePreview={imagePreview} />
								{error && <p className="mt-2 text-sm text-red-500">{error}</p>}
							</div>

							<Button
								type="submit"
								disabled={loading}
								className="w-full py-6 text-lg  text-white bg-primary hover:from-primary-600 hover:to-primary transition-all duration-300"
							>
								{loading ? 'Adding...' : 'Add Product'}
							</Button>
						</form>
					</CardContent>
				</Card>
			</TabsContent>
			<TabsContent value="preview">
				<Card>
					<CardHeader>
						<CardTitle>Product Display</CardTitle>
						<CardDescription>
							If it looks right, its gonna be good.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-2">
						<PreviewCard product={newProduct} image={imagePreview} />
					</CardContent>
				</Card>
			</TabsContent>
		</Tabs>
	)
}

// <---- UI Components ---->

// Custom hook for drag and drop
const useDragAndDrop = (onFileDrop) => {
	const [isDragging, setIsDragging] = useState(false);
	const dropZoneRef = useRef(null);

	const handleDrag = (e) => {
		e.preventDefault();
		e.stopPropagation();
	};

	const handleDragIn = (e) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(true);
	};

	const handleDragOut = (e) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(false);
	};

	const handleDrop = (e) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(false);

		const files = e.dataTransfer.files;
		if (files?.length > 0) {
			onFileDrop(files[0]);
		}
	};

	useEffect(() => {
		const dropZone = dropZoneRef.current;
		if (!dropZone) return;

		dropZone.addEventListener('dragenter', handleDragIn);
		dropZone.addEventListener('dragleave', handleDragOut);
		dropZone.addEventListener('dragover', handleDrag);
		dropZone.addEventListener('drop', handleDrop);

		return () => {
			dropZone.removeEventListener('dragenter', handleDragIn);
			dropZone.removeEventListener('dragleave', handleDragOut);
			dropZone.removeEventListener('dragover', handleDrag);
			dropZone.removeEventListener('drop', handleDrop);
		};
	}, [onFileDrop]);

	return { isDragging, dropZoneRef };
};

// ImageUploader Component
const ImageUploader = ({ onImageUpload, imagePreview }) => {
	const fileInputRef = useRef(null);
	const { isDragging, dropZoneRef } = useDragAndDrop(onImageUpload);

	return (
		<div
			ref={dropZoneRef}
			onClick={() => fileInputRef.current?.click()}
			className={`
        mt-2 p-8 border-2 border-dashed rounded-2xl cursor-pointer
        backdrop-blur-sm backdrop-filter
        transition-all duration-300 ease-in-out
        ${isDragging
					? 'border-primary bg-primary/10 scale-102'
					: 'border-gray-300 hover:border-primary hover:shadow-lg dark:border-gray-600'
				}
        ${imagePreview ? 'pb-4' : 'flex flex-col items-center justify-center min-h-[240px]'}
        group
      `}
		>
			<input
				type="file"
				ref={fileInputRef}
				className="hidden"
				accept="image/*"
				onChange={(e) => onImageUpload(e.target.files?.[0])}
			/>

			{imagePreview ? (
				<div className="space-y-4 relative group">
					<div className="overflow-hidden rounded-xl">
						<img
							src={imagePreview}
							alt="Preview"
							className="mx-auto max-h-[240px] object-contain transform transition-transform group-hover:scale-105"
						/>
					</div>
					<p className="text-sm text-center text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity">
						Click or drag to replace image
					</p>
				</div>
			) : (
				<div className="transform transition-transform group-hover:scale-110">
					<FaCloudUploadAlt className="w-16 h-16 text-gray-400 mb-6 group-hover:text-primary transition-quantity_types" />
					<p className="text-lg text-gray-600 text-center mb-2 dark:text-gray-300">
						Drag and drop your image here
					</p>
					<p className="text-sm text-gray-500 text-center dark:text-gray-400">
						or click to select a file
					</p>
				</div>
			)}
		</div>
	);
};
