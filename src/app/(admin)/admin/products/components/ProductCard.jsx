import Image from 'next/image';
import { FaTrash } from 'react-icons/fa';

// productCard Component
const ProductCard = ({ product, onDelete }) => (
	<div className="group relative bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
		<div className="relative h-32">
			{product?.Images[0]?.url ? (
				<Image
					src={product?.Images[0].url}
					alt={product.Name}
					width={200}
					height={200}
					className="w-full h-full object-contain"
				/>
			) : (
				<div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center dark:from-gray-700 dark:to-gray-800">
					<span className="text-gray-400">No image</span>
				</div>
			)}
			<button
				onClick={() => onDelete(product._id)}
				className="absolute top-2 right-2 p-3 bg-red-500/90 text-white rounded-full opacity-0 group-hover:opacity-100 transform translate-x-full group-hover:translate-x-0 transition-all duration-300 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
			>
				<FaTrash size={16} />
			</button>
		</div>
		<div className="p-4 bg-gradient-to-b from-transparent to-white/5 backdrop-blur-sm">
			<h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
				{product.Name}
			</h3>
			<p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
				{product.Description}
			</p>
		</div>
	</div>
);
export default ProductCard;
