'use client';

import { useState, useEffect, useRef } from 'react';
import { FaTrash, FaCloudUploadAlt } from 'react-icons/fa';
import { Label } from '@/components/ui/label';
import { Classes } from "@/components/ui/input";
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { Eye, EyeOff } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { ToastAction } from "@/components/ui/toast";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";

// Constants
const INITIAL_CATEGORY = {
	first_name: '',
	last_name: '',
	email: '',
	password: ''
};

// Card Component
const UserCard = ({ user, onDelete }) => (
	<div className="group relative bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
		<div className="relative h-6">

			<button
				onClick={() => onDelete(user?._id)}
				className="absolute top-2 right-2 p-3 bg-red-500/90 text-white rounded-full opacity-0 group-hover:opacity-100 transform translate-x-full group-hover:translate-x-0 transition-all duration-300 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
			>
				<FaTrash size={16} />
			</button>
		</div>
		<div className="p-4">
			<h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
				{user?.FirstName} {user?.LastName}
			</h3>
			<p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
				{user?.Email}
			</p>
			<Dialog>
				<DialogTrigger asChild>
					<Button className='mt-1 text-primary border-primary hover:bg-primary hover:text-white' variant='outline'> Change Password </Button>
				</DialogTrigger>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Change Password Info</DialogTitle>
					</DialogHeader>
					<div>
						<EditPassword email={user?.Email} />
					</div>
				</DialogContent>
			</Dialog>
		</div>

	</div>
);

function EditPassword({ email }) {
	const router = useRouter();
	const [Password, setPassword] = useState('');
	const [showPassword, setshowPassword] = useState(false);
	const [error, setError] = useState('');
	const [loading, setLoading] = useState(false);

	const handleSubmit = async (e) => {
		if (!confirm('Are you sure you want to change password as this action will not be reversible?')) return;
		e.preventDefault();
		setLoading(true);
		setError('');

		try {
			const response = await fetch('/api/admin/users', {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ email, password: Password }),
			});
			const data = await response.json();
			if (data.returncode === 200) {
				toast({
					title: 'Success',
					description: data.message,
					action: <ToastAction altText="Okay"> Okay </ToastAction>,
				});
			}
			else {
				toast({
					title: 'Error',
					description: data.message,
					action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
				});
			}
			resetForm();
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Temporary Server Error, Please try again later...',
				action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
			});
		} finally {
			setLoading(false);
			router.refresh();
		}
	};

	return (
		<>
			<div>
				<Label htmlFor="Password" className="text-lg">
					Password
				</Label>
				<div className='flex gap-2'>
					<input
						type={`${showPassword ? "text" : "password"}`}
						value={Password}
						onChange={(e) => {
							e.preventDefault();
							setPassword(e.target.value);
						}}
						className={Classes}
						placeholder='******'
					/>
					<Button
						onClick={(e) => { e.preventDefault(); setshowPassword(!showPassword); }}
					>
						{showPassword ? <EyeOff /> : <Eye />}
					</Button>
				</div>
			</div>

			<div className='mt-4 w-full'>
				<Button
					disabled={loading}
					onClick={handleSubmit}
					className="w-full py-6 text-lg  text-white bg-primary hover:from-primary-600 hover:to-primary transition-all duration-300"
				>
					{loading ? 'Changing...' : 'Change'}
				</Button>
			</div>

		</>
	);
}

export default function UsersPage() {
	const router = useRouter();
	const { toast } = useToast();
	const [users, setUsers] = useState([]);
	const [newUser, setNewUser] = useState(INITIAL_CATEGORY);
	const [showPassword, setshowPassword] = useState(false);
	const [error, setError] = useState('');
	const [loading, setLoading] = useState(false);

	useEffect(() => {
		fetchusers();
	}, []);

	const fetchusers = async () => {
		setLoading(true);
		try {
			const response = await fetch('/api/admin/users');
			const data = await response.json();
			if (response.ok) {
				setUsers(data.output);
			}
		} catch (error) {
			console.error('Error fetching users:', error);
		} finally {
			setLoading(false);
		}
	};

	const resetForm = () => {
		setNewUser(INITIAL_CATEGORY);
		setError('');
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setLoading(true);
		setError('');

		try {
			const response = await fetch('/api/admin/users', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(newUser),
			});
			const data = await response.json();
			if (data.returncode === 200) {
				toast({
					title: 'Success',
					description: data.message,
					action: <ToastAction altText="Okay"> Okay </ToastAction>,
				});
			}
			else {
				toast({
					title: 'Error',
					description: data.message,
					action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
				});
			}
			resetForm();
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Temporary Server Error, Please try again later...',
				action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
			});
		} finally {
			setLoading(false);
			router.refresh();
		}
	};

	const handleDelete = async (id) => {
		if (!confirm('Are you sure you want to delete this category?')) return;

		try {
			const response = await fetch(`/api/admin/users?id=${id}`, {
				method: 'DELETE',
			});

			const data = await response.json();
			if (data.returncode === 200) {
				toast({
					title: 'Success',
					description: data.message,
					action: <ToastAction altText="Okay"> Okay </ToastAction>,
				});
			}
			else {
				toast({
					title: 'Error',
					description: data.message,
					action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
				});
			}
			await fetchusers();
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Temporary Server Error, Please try again later...',
				action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
			});
		}
	};

	return (
		<section className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
			<div className="backdrop-blur-lg backdrop-filter">
				<h1 className="text-4xl font-bold p-6 text-center text-white bg-primary">
					Manage Users
				</h1>
			</div>

			<div className="container mx-auto px-4 py-8">
				<div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-2xl shadow-xl p-8 mb-12">
					<h2 className="text-2xl font-semibold mb-8 text-primary">Add New User</h2>
					<form onSubmit={handleSubmit} className="space-y-6">
						<div className="grid gap-6 md:grid-cols-2">
							<div>
								<Label htmlFor="firstName" className="text-lg">First Name</Label>
								<input
									type="text"
									value={newUser.first_name}
									onChange={(e) => setNewUser({ ...newUser, first_name: e.target.value })}
									className={`${Classes} mt-2 backdrop-blur-sm`}
									placeholder='eg; John'
									required
								/>
							</div>

							<div>
								<Label htmlFor="lastName" className="text-lg">
									Last Name
								</Label>
								<input
									type="text"
									value={newUser.last_name}
									onChange={(e) => setNewUser({ ...newUser, last_name: e.target.value })}
									className={`${Classes} mt-2 backdrop-blur-sm`}
									placeholder='eg; Doe'
									required
								/>
							</div>
						</div>

						<div>
							<Label htmlFor="Email" className="text-lg">Email</Label>
							<input
								type="email"
								value={newUser.email}
								onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
								className={`${Classes} mt-2 backdrop-blur-sm`}
								placeholder='eg; <EMAIL>'
							/>
						</div>

						<div>
							<Label htmlFor="Password" className="text-lg">
								Password
							</Label>
							<div className='flex gap-2'>
								<input
									type={`${showPassword ? "text" : "password"}`}
									value={newUser.password}
									onChange={(e) => {
										e.preventDefault();
										setNewUser({ ...newUser, password: e.target.value });
									}}
									className={Classes}
									placeholder='******'
								/>
								<Button
									onClick={(e) => { e.preventDefault(); setshowPassword(!showPassword); }}
								>
									{showPassword ? <EyeOff /> : <Eye />}
								</Button>
							</div>
						</div>



						<Button
							type="submit"
							disabled={loading}
							className="w-full py-6 text-lg  text-white bg-primary hover:from-primary-600 hover:to-primary transition-all duration-300"
						>
							{loading ? 'Adding...' : 'Add User'}
						</Button>
					</form>
				</div>

				{loading ? (
					<div className="flex justify-center items-center h-48">
						<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
					</div>
				) : (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
						{users.map((user, index) => (
							<UserCard
								key={index}
								user={user}
								onDelete={handleDelete}
							/>
						))}
					</div>
				)}
			</div>
		</section>
	);
}
