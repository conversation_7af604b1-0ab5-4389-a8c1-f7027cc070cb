"use client";
import { useEffect, useState } from "react";
import AdminNavbar from "./AdminNavbar";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "@/app/contexts/Auth";
import { animated_logo } from "@/app/components/Logo";

export default function AdminRootSkeleton({ children }) {
	const pathname = usePathname();
	const [show, setshow] = useState(false); // Loading
	const [Show, setShow] = useState(false); // Auth
	const router = useRouter();

	// Move the auth check inside AuthContextProvider's children
	const AuthenticatedContent = () => {
		const { admin } = useAuth();

		useEffect(() => {
			if (!admin && !pathname.startsWith("/admin/auth")) {
				router.push('/admin/auth');
			} else if (admin && pathname.startsWith("/admin/auth")) {
				router.push('/admin/product_categories');
			}
		}, [admin]);

		return (
			<>
				{Show && <AdminNavbar />}
				{children}
			</>
		);
	};

	useEffect(() => {
		setShow(!pathname.startsWith("/admin/auth"));
		const timer = setTimeout(() => {
			setshow(true);
		}, 3000);

		return () => clearTimeout(timer);
	}, [pathname]);

	return (
		<>

			{!show ? (
				<div className="flex items-center justify-center h-screen">
					{animated_logo}
				</div>
			) : (
				<AuthenticatedContent />
			)}
		</>
	);
}
