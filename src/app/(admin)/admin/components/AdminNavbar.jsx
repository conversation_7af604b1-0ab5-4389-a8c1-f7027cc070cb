'use client';

import { logo } from '@/app/components/Logo';
import Link from 'next/link';
import React, { useEffect, useState } from 'react'
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Box, Layers, LayoutList, LogOutIcon, UserCircleIcon, UserCog } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/contexts/Auth';

function AdminNavbar() {
	const { admin, adminLogout } = useAuth();
	const [Admin, setAdmin] = useState('');
	const router = useRouter();

	useEffect(() => {
		if (admin?.firstName && admin?.lastName) {
			setAdmin(admin.firstName + " " + admin.lastName);
		}
	}, [Admin])


	return (
		<nav className='p-5 shadow-sm flex items-center justify-between'>
			<div className='flex items-center gap-8 '>

				<Link href='/admin' className="block text-teal-600">
					{logo}
				</Link>
			</div>
			<div className='flex gap-5 items-center'>
				{
					admin && (
						<DropdownMenu>
							<DropdownMenuTrigger className='flex items-center gap-3 outline-none'>
								<UserCircleIcon className='h-10 w-10 text-primary bg-teal-100 p-1 rounded-full cursor-pointer' />
								{Admin}
							</DropdownMenuTrigger>
							<DropdownMenuContent className='px-4'>
								<DropdownMenuLabel>Admin Menu</DropdownMenuLabel>
								<DropdownMenuSeparator />
								<DropdownMenuItem
									onClick={() => { router.push('/admin/users') }}
									className='cursor-pointer flex gap-3'
								>
									<UserCog className='text-gray-400 w-8 h-8' />
									<p>	Users </p>
								</DropdownMenuItem>
								<DropdownMenuItem
									onClick={() => { router.push('/admin/slides') }}
									className='cursor-pointer flex gap-3'
								>
									<LayoutList className='text-gray-400 w-8 h-8' />
									<p>	Slides </p>
								</DropdownMenuItem>
								<DropdownMenuItem
									onClick={() => { router.push('/admin/product_categories') }}
									className='cursor-pointer flex gap-3'
								>
									<Layers className='text-gray-400 w-8 h-8' />
									<p> Product Categories </p>
								</DropdownMenuItem>
								<DropdownMenuItem
									onClick={() => { router.push('/admin/products') }}
									className='cursor-pointer flex gap-3'
								>
									<Box className='text-gray-400 w-8 h-8' />
									<p> Products </p>
								</DropdownMenuItem>
								<DropdownMenuSeparator />
								<DropdownMenuItem
									onClick={adminLogout}
									className='cursor-pointer flex justify-between'
								>
									Logout <LogOutIcon className='text-gray-400' />
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					)}
			</div>
		</nav>
	)
}

export default AdminNavbar
