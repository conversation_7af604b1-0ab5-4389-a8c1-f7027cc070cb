'use client';

import { useState, useEffect, useRef } from 'react';
import { FaTrash, FaCloudUploadAlt } from 'react-icons/fa';
import Image from 'next/image';
import { Label } from '@/components/ui/label';
import { Classes } from "@/components/ui/input";
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

// Constants
const INITIAL_CATEGORY = {
	CategoryName: '',
	CategoryDesc: '',
	color: '',
	Image: []
};

// Custom hook for drag and drop
const useDragAndDrop = (onFileDrop) => {
	const [isDragging, setIsDragging] = useState(false);
	const dropZoneRef = useRef(null);

	const handleDrag = (e) => {
		e.preventDefault();
		e.stopPropagation();
	};

	const handleDragIn = (e) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(true);
	};

	const handleDragOut = (e) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(false);
	};

	const handleDrop = (e) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(false);

		const files = e.dataTransfer.files;
		if (files?.length > 0) {
			onFileDrop(files[0]);
		}
	};

	useEffect(() => {
		const dropZone = dropZoneRef.current;
		if (!dropZone) return;

		dropZone.addEventListener('dragenter', handleDragIn);
		dropZone.addEventListener('dragleave', handleDragOut);
		dropZone.addEventListener('dragover', handleDrag);
		dropZone.addEventListener('drop', handleDrop);

		return () => {
			dropZone.removeEventListener('dragenter', handleDragIn);
			dropZone.removeEventListener('dragleave', handleDragOut);
			dropZone.removeEventListener('dragover', handleDrag);
			dropZone.removeEventListener('drop', handleDrop);
		};
	}, [onFileDrop]);

	return { isDragging, dropZoneRef };
};

// ImageUploader Component
const ImageUploader = ({ onImageUpload, imagePreview }) => {
	const fileInputRef = useRef(null);
	const { isDragging, dropZoneRef } = useDragAndDrop(onImageUpload);

	return (
		<div
			ref={dropZoneRef}
			onClick={() => fileInputRef.current?.click()}
			className={`
        mt-2 p-8 border-2 border-dashed rounded-2xl cursor-pointer
        backdrop-blur-sm backdrop-filter
        transition-all duration-300 ease-in-out
        ${isDragging
					? 'border-primary bg-primary/10 scale-102'
					: 'border-gray-300 hover:border-primary hover:shadow-lg dark:border-gray-600'
				}
        ${imagePreview ? 'pb-4' : 'flex flex-col items-center justify-center min-h-[240px]'}
        group
      `}
		>
			<input
				type="file"
				ref={fileInputRef}
				className="hidden"
				accept="image/*"
				onChange={(e) => onImageUpload(e.target.files?.[0])}
			/>

			{imagePreview ? (
				<div className="space-y-4 relative group">
					<div className="overflow-hidden rounded-xl">
						<img
							src={imagePreview}
							alt="Preview"
							className="mx-auto max-h-[240px] object-contain transform transition-transform group-hover:scale-105"
						/>
					</div>
					<p className="text-sm text-center text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity">
						Click or drag to replace image
					</p>
				</div>
			) : (
				<div className="transform transition-transform group-hover:scale-110">
					<FaCloudUploadAlt className="w-16 h-16 text-gray-400 mb-6 group-hover:text-primary transition-colors" />
					<p className="text-lg text-gray-600 text-center mb-2 dark:text-gray-300">
						Drag and drop your image here
					</p>
					<p className="text-sm text-gray-500 text-center dark:text-gray-400">
						or click to select a file
					</p>
				</div>
			)}
		</div>
	);
};

// CategoryCard Component
const CategoryCard = ({ category, onDelete }) => (
	<div className="group relative bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
		<div className="relative h-32">
			{category.Icon[0]?.url ? (
				<Image
					src={category.Icon[0].url}
					alt={category.Name}
					width={200}
					height={200}
					className="w-full h-full object-contain"
				/>
			) : (
				<div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center dark:from-gray-700 dark:to-gray-800">
					<span className="text-gray-400">No image</span>
				</div>
			)}
			<button
				onClick={() => onDelete(category._id)}
				className="absolute top-2 right-2 p-3 bg-red-500/90 text-white rounded-full opacity-0 group-hover:opacity-100 transform translate-x-full group-hover:translate-x-0 transition-all duration-300 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
			>
				<FaTrash size={16} />
			</button>
		</div>
		<div className="p-4 bg-gradient-to-b from-transparent to-white/5 backdrop-blur-sm">
			<h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
				{category.Name}
			</h3>
			<p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
				{category.Description}
			</p>
		</div>
	</div>
);

export default function CategoriesPage() {
	const router = useRouter();
	const [categories, setCategories] = useState([]);
	const [newCategory, setNewCategory] = useState(INITIAL_CATEGORY);
	const [imageFile, setImageFile] = useState(null);
	const [imagePreview, setImagePreview] = useState(null);
	const [error, setError] = useState('');
	const [loading, setLoading] = useState(false);

	useEffect(() => {
		fetchCategories();
	}, []);

	const fetchCategories = async () => {
		setLoading(true);
		try {
			const response = await fetch('/api/admin/product_categories');
			const data = await response.json();
			if (response.ok) {
				setCategories(data.output);
			}
		} catch (error) {
			console.error('Error fetching categories:', error);
		} finally {
			setLoading(false);
		}
	};

	const handleImageUpload = (file) => {
		if (!file) return;
		if (!file.type.startsWith('image/')) {
			setError('Please upload an image file');
			return;
		}

		setError('');
		setImageFile(file);
		const reader = new FileReader();
		reader.onloadend = () => setImagePreview(reader.result);
		reader.readAsDataURL(file);
	};

	const resetForm = () => {
		setNewCategory(INITIAL_CATEGORY);
		setImageFile(null);
		setImagePreview(null);
		setError('');
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setLoading(true);
		setError('');

		try {
			if (!newCategory.CategoryName) throw new Error('Please fill in all required fields');
			if (!imageFile) throw new Error('Please select an image');

			const base64Data = await new Promise((resolve) => {
				const reader = new FileReader();
				reader.onloadend = () => resolve(reader.result);
				reader.readAsDataURL(imageFile);
			});

			const response = await fetch('/api/admin/product_categories', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					category_name: newCategory.CategoryName,
					description: newCategory.CategoryDesc,
					image: {
						name: imageFile.name,
						contentType: imageFile.type,
						base64Data,
						description: ''
					},
					color: newCategory.color
				}),
			});

			if (!response.ok) {
				throw new Error('Failed to add category');
			}

			resetForm();
			await fetchCategories();
		} catch (error) {
			setError(error.message);
		} finally {
			setLoading(false);
			router.refresh();
		}
	};

	const handleDelete = async (id) => {
		if (!confirm('Are you sure you want to delete this category?')) return;

		try {
			const response = await fetch(`/api/admin/product_categories?id=${id}`, {
				method: 'DELETE',
			});

			if (!response.ok) throw new Error('Failed to delete category');
			await fetchCategories();
		} catch (error) {
			console.error('Error deleting category:', error);
		}
	};

	return (
		<section className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
			<div className="backdrop-blur-lg backdrop-filter">
				<h1 className="text-4xl font-bold p-6 text-center text-white bg-primary">
					Manage Product Categories
				</h1>
			</div>

			<div className="container mx-auto px-4 py-8">
				<div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-2xl shadow-xl p-8 mb-12">
					<h2 className="text-2xl font-semibold mb-8 text-primary">Add New Category</h2>
					<form onSubmit={handleSubmit} className="space-y-6">
						<div className="grid gap-6 md:grid-cols-2">
							<div>
								<Label htmlFor="categoryName" className="text-lg">Category Name</Label>
								<input
									type="text"
									id="categoryName"
									value={newCategory.CategoryName}
									onChange={(e) => setNewCategory({ ...newCategory, CategoryName: e.target.value })}
									className={`${Classes} mt-2 backdrop-blur-sm`}
									required
								/>
							</div>

							<div>
								<Label htmlFor="categoryColor" className="text-lg">Category Color</Label>
								<input
									type="text"
									id="categoryColor"
									value={newCategory.color}
									onChange={(e) => setNewCategory({ ...newCategory, color: e.target.value })}
									className={`${Classes} mt-2 backdrop-blur-sm`}
								/>
							</div>
						</div>

						<div>
							<Label htmlFor="categoryDesc" className="text-lg">Category Description</Label>
							<textarea
								id="categoryDesc"
								value={newCategory.CategoryDesc}
								onChange={(e) => setNewCategory({ ...newCategory, CategoryDesc: e.target.value })}
								rows={3}
								className={`${Classes} mt-2 backdrop-blur-sm`}
							/>
						</div>

						<div>
							<Label htmlFor="categoryImage" className="text-lg">Category Image</Label>
							<ImageUploader onImageUpload={handleImageUpload} imagePreview={imagePreview} />
							{error && <p className="mt-2 text-sm text-red-500">{error}</p>}
						</div>

						<Button
							type="submit"
							disabled={loading}
							className="w-full py-6 text-lg  text-white bg-primary hover:from-primary-600 hover:to-primary transition-all duration-300"
						>
							{loading ? 'Adding...' : 'Add Category'}
						</Button>
					</form>
				</div>

				{loading ? (
					<div className="flex justify-center items-center h-48">
						<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
					</div>
				) : (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
						{categories.map((category) => (
							<CategoryCard
								key={category._id}
								category={category}
								onDelete={handleDelete}
							/>
						))}
					</div>
				)}
			</div>
		</section>
	);
}
