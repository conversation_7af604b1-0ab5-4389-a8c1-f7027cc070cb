'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/app/contexts/Auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import { ToastAction } from '@/components/ui/toast';
import { Loader2, Package, Calendar, MapPin, CreditCard, Truck, Eye, Edit, User } from 'lucide-react';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";

export default function AdminOrdersPage() {
	const { admin } = useAuth();
	const [orders, setOrders] = useState([]);
	const [loading, setLoading] = useState(false);
	const [selectedOrder, setSelectedOrder] = useState(null);
	const [updating, setUpdating] = useState(false);

	useEffect(() => {
		if (admin) {
			fetchOrders();
		}
	}, [admin]);

	const fetchOrders = async () => {
		try {
			setLoading(true);
			const response = await fetch('/api/orders');
			const data = await response.json();
			
			if (data.returncode === 200) {
				setOrders(data.output || []);
			} else {
				toast({
					title: 'Error',
					description: data.message || 'Failed to fetch orders',
					action: <ToastAction altText="Try Again">Try Again</ToastAction>,
				});
			}
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Failed to fetch orders',
				action: <ToastAction altText="Try Again">Try Again</ToastAction>,
			});
		} finally {
			setLoading(false);
		}
	};

	const updateOrderStatus = async (orderId, newStatus, statusType) => {
		try {
			setUpdating(true);
			const updateData = {
				order_id: orderId,
				[statusType]: newStatus
			};

			const response = await fetch('/api/orders', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(updateData),
			});

			const data = await response.json();

			if (data.returncode === 200) {
				toast({
					title: 'Success',
					description: `${statusType === 'order_status' ? 'Order' : 'Payment'} status updated successfully`,
				});
				await fetchOrders();
			} else {
				toast({
					title: 'Error',
					description: data.message || 'Failed to update status',
					action: <ToastAction altText="Try Again">Try Again</ToastAction>,
				});
			}
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Failed to update status',
				action: <ToastAction altText="Try Again">Try Again</ToastAction>,
			});
		} finally {
			setUpdating(false);
		}
	};

	const getStatusColor = (status) => {
		switch (status?.toLowerCase()) {
			case 'order placed':
				return 'bg-blue-100 text-blue-800';
			case 'processing':
				return 'bg-yellow-100 text-yellow-800';
			case 'shipped':
				return 'bg-purple-100 text-purple-800';
			case 'delivered':
				return 'bg-green-100 text-green-800';
			case 'cancelled':
				return 'bg-red-100 text-red-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	const getPaymentStatusColor = (status) => {
		switch (status?.toLowerCase()) {
			case 'paid':
				return 'bg-green-100 text-green-800';
			case 'pending':
				return 'bg-yellow-100 text-yellow-800';
			case 'failed':
				return 'bg-red-100 text-red-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	const calculateOrderTotal = (cart) => {
		return cart?.reduce((total, item) => total + (item.Price || 0), 0) || 0;
	};

	const calculateOrderSavings = (cart) => {
		return cart?.reduce((total, item) => total + (item.Savings || 0), 0) || 0;
	};

	if (!admin) {
		return (
			<div className="container mx-auto px-4 py-8">
				<Card>
					<CardContent className="flex items-center justify-center py-8">
						<p>Please log in as admin to view orders.</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="container mx-auto px-4 py-8">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Package className="h-5 w-5" />
						Orders Management
					</CardTitle>
					<CardDescription>
						View and manage all customer orders
					</CardDescription>
				</CardHeader>
				<CardContent>
					{loading ? (
						<div className="flex items-center justify-center py-8">
							<Loader2 className="h-8 w-8 animate-spin" />
						</div>
					) : orders.length === 0 ? (
						<div className="text-center py-8">
							<Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
							<p className="text-gray-500">No orders found</p>
						</div>
					) : (
						<div className="space-y-4">
							<div className="rounded-md border">
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Order ID</TableHead>
											<TableHead>Customer</TableHead>
											<TableHead>Date</TableHead>
											<TableHead>Amount</TableHead>
											<TableHead>Order Status</TableHead>
											<TableHead>Payment Status</TableHead>
											<TableHead>Actions</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{orders.map((order) => (
											<TableRow key={order._id}>
												<TableCell className="font-medium">
													#{order._id.slice(-8)}
												</TableCell>
												<TableCell>
													<div>
														<p className="font-medium">{order.Name}</p>
														<p className="text-sm text-gray-500">{order.Contact}</p>
													</div>
												</TableCell>
												<TableCell>
													{new Date(order.createdAt).toLocaleDateString()}
												</TableCell>
												<TableCell>
													<div>
														<p className="font-semibold">₹{calculateOrderTotal(order.Cart)}</p>
														{calculateOrderSavings(order.Cart) > 0 && (
															<p className="text-xs text-green-600">
																Saved ₹{calculateOrderSavings(order.Cart)}
															</p>
														)}
													</div>
												</TableCell>
												<TableCell>
													<Select
														value={order.OrderStatus}
														onValueChange={(value) => updateOrderStatus(order._id, value, 'order_status')}
														disabled={updating}
													>
														<SelectTrigger className="w-32">
															<SelectValue />
														</SelectTrigger>
														<SelectContent>
															<SelectItem value="Order Placed">Order Placed</SelectItem>
															<SelectItem value="Processing">Processing</SelectItem>
															<SelectItem value="Shipped">Shipped</SelectItem>
															<SelectItem value="Delivered">Delivered</SelectItem>
															<SelectItem value="Cancelled">Cancelled</SelectItem>
														</SelectContent>
													</Select>
												</TableCell>
												<TableCell>
													<Select
														value={order.PaymentStatus}
														onValueChange={(value) => updateOrderStatus(order._id, value, 'payment_status')}
														disabled={updating}
													>
														<SelectTrigger className="w-24">
															<SelectValue />
														</SelectTrigger>
														<SelectContent>
															<SelectItem value="Paid">Paid</SelectItem>
															<SelectItem value="Pending">Pending</SelectItem>
															<SelectItem value="Failed">Failed</SelectItem>
														</SelectContent>
													</Select>
												</TableCell>
												<TableCell>
													<Dialog>
														<DialogTrigger asChild>
															<Button 
																variant="outline" 
																size="sm"
																onClick={() => setSelectedOrder(order)}
															>
																<Eye className="h-4 w-4" />
															</Button>
														</DialogTrigger>
														<DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
															<DialogHeader>
																<DialogTitle>Order Details</DialogTitle>
																<DialogDescription>
																	Order #{selectedOrder?._id.slice(-8)}
																</DialogDescription>
															</DialogHeader>
															{selectedOrder && (
																<div className="space-y-6">
																	<div className="grid grid-cols-2 gap-6">
																		<div>
																			<h4 className="font-semibold mb-3 flex items-center gap-2">
																				<Package className="h-4 w-4" />
																				Order Information
																			</h4>
																			<div className="space-y-2 text-sm">
																				<p><span className="font-medium">Order ID:</span> #{selectedOrder._id}</p>
																				<p><span className="font-medium">Order Date:</span> {new Date(selectedOrder.createdAt).toLocaleDateString()}</p>
																				<p><span className="font-medium">Status:</span> 
																					<Badge className={`ml-2 ${getStatusColor(selectedOrder.OrderStatus)}`}>
																						{selectedOrder.OrderStatus}
																					</Badge>
																				</p>
																				<p><span className="font-medium">Payment:</span> 
																					<Badge className={`ml-2 ${getPaymentStatusColor(selectedOrder.PaymentStatus)}`}>
																						{selectedOrder.PaymentStatus}
																					</Badge>
																				</p>
																				<p><span className="font-medium">Payment Mode:</span> {selectedOrder.PaymentMode}</p>
																			</div>
																		</div>
																		<div>
																			<h4 className="font-semibold mb-3 flex items-center gap-2">
																				<User className="h-4 w-4" />
																				Customer & Delivery
																			</h4>
																			<div className="text-sm space-y-1">
																				<p className="font-medium">{selectedOrder.Name}</p>
																				<p>{selectedOrder.Contact}</p>
																				<div className="mt-2">
																					<p className="font-medium">Delivery Address:</p>
																					<p>{selectedOrder.Apartment}</p>
																					<p>{selectedOrder.StreetAddress}</p>
																					<p>{selectedOrder.Landmark}</p>
																					<p>{selectedOrder.City} - {selectedOrder.Pincode}</p>
																				</div>
																			</div>
																		</div>
																	</div>
																	
																	<div>
																		<h4 className="font-semibold mb-3">Order Items</h4>
																		<div className="space-y-2">
																			{selectedOrder.Cart?.map((item, index) => (
																				<div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded border">
																					<div>
																						<p className="font-medium">Product ID: {item.ProductId}</p>
																						<p className="text-sm text-gray-600">Quantity: {item.Quantity}</p>
																					</div>
																					<div className="text-right">
																						<p className="font-semibold">₹{item.Price}</p>
																						{item.Savings > 0 && (
																							<p className="text-sm text-green-600">Saved ₹{item.Savings}</p>
																						)}
																					</div>
																				</div>
																			))}
																		</div>
																	</div>

																	<div className="border-t pt-4">
																		<div className="flex justify-between items-center text-lg">
																			<span className="font-semibold">Total Amount:</span>
																			<span className="font-bold">₹{calculateOrderTotal(selectedOrder.Cart)}</span>
																		</div>
																		{calculateOrderSavings(selectedOrder.Cart) > 0 && (
																			<div className="flex justify-between items-center text-green-600">
																				<span>Total Savings:</span>
																				<span className="font-semibold">₹{calculateOrderSavings(selectedOrder.Cart)}</span>
																			</div>
																		)}
																	</div>
																</div>
															)}
														</DialogContent>
													</Dialog>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
