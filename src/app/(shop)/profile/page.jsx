'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/app/contexts/Auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import { ToastAction } from '@/components/ui/toast';
import { Loader2, User, Mail, Calendar, Shield } from 'lucide-react';

export default function ProfilePage() {
	const { user, checkAuth } = useAuth();
	const [loading, setLoading] = useState(false);
	const [editing, setEditing] = useState(false);
	const [profileData, setProfileData] = useState({
		FirstName: '',
		LastName: '',
		Email: '',
		createdAt: '',
		lastLogin: '',
		isRegular: false
	});
	const [formData, setFormData] = useState({
		first_name: '',
		last_name: '',
		email: ''
	});

	useEffect(() => {
		if (user) {
			fetchUserProfile();
		}
	}, [user]);

	const fetchUserProfile = async () => {
		try {
			setLoading(true);
			const response = await fetch('/api/users');
			const data = await response.json();
			
			if (data.returncode === 200 && data.output.length > 0) {
				const profile = data.output[0];
				setProfileData(profile);
				setFormData({
					first_name: profile.FirstName,
					last_name: profile.LastName,
					email: profile.Email
				});
			} else {
				toast({
					title: 'Error',
					description: data.message || 'Failed to fetch profile',
					action: <ToastAction altText="Try Again">Try Again</ToastAction>,
				});
			}
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Failed to fetch profile data',
				action: <ToastAction altText="Try Again">Try Again</ToastAction>,
			});
		} finally {
			setLoading(false);
		}
	};

	const handleInputChange = (e) => {
		const { name, value } = e.target;
		setFormData(prev => ({
			...prev,
			[name]: value
		}));
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setLoading(true);

		try {
			const response = await fetch('/api/users', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(formData),
			});

			const data = await response.json();

			if (data.returncode === 200) {
				toast({
					title: 'Success',
					description: 'Profile updated successfully',
				});
				setEditing(false);
				await fetchUserProfile();
				await checkAuth(); // Refresh auth context
			} else {
				toast({
					title: 'Error',
					description: data.message || 'Failed to update profile',
					action: <ToastAction altText="Try Again">Try Again</ToastAction>,
				});
			}
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Failed to update profile',
				action: <ToastAction altText="Try Again">Try Again</ToastAction>,
			});
		} finally {
			setLoading(false);
		}
	};

	const handleCancel = () => {
		setFormData({
			first_name: profileData.FirstName,
			last_name: profileData.LastName,
			email: profileData.Email
		});
		setEditing(false);
	};

	if (!user) {
		return (
			<div className="container mx-auto px-4 py-8">
				<Card>
					<CardContent className="flex items-center justify-center py-8">
						<p>Please log in to view your profile.</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="container mx-auto px-4 py-8 max-w-2xl">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<User className="h-5 w-5" />
						My Profile
					</CardTitle>
					<CardDescription>
						Manage your personal information and account settings
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-6">
					{loading && !editing ? (
						<div className="flex items-center justify-center py-8">
							<Loader2 className="h-8 w-8 animate-spin" />
						</div>
					) : (
						<>
							{editing ? (
								<form onSubmit={handleSubmit} className="space-y-4">
									<div className="grid grid-cols-2 gap-4">
										<div className="space-y-2">
											<Label htmlFor="first_name">First Name</Label>
											<Input
												id="first_name"
												name="first_name"
												value={formData.first_name}
												onChange={handleInputChange}
												required
											/>
										</div>
										<div className="space-y-2">
											<Label htmlFor="last_name">Last Name</Label>
											<Input
												id="last_name"
												name="last_name"
												value={formData.last_name}
												onChange={handleInputChange}
												required
											/>
										</div>
									</div>
									<div className="space-y-2">
										<Label htmlFor="email">Email</Label>
										<Input
											id="email"
											name="email"
											type="email"
											value={formData.email}
											onChange={handleInputChange}
											required
										/>
									</div>
									<div className="flex gap-2">
										<Button type="submit" disabled={loading}>
											{loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
											Save Changes
										</Button>
										<Button type="button" variant="outline" onClick={handleCancel}>
											Cancel
										</Button>
									</div>
								</form>
							) : (
								<div className="space-y-4">
									<div className="grid grid-cols-2 gap-4">
										<div>
											<Label className="text-sm font-medium text-gray-500">First Name</Label>
											<p className="text-lg">{profileData.FirstName}</p>
										</div>
										<div>
											<Label className="text-sm font-medium text-gray-500">Last Name</Label>
											<p className="text-lg">{profileData.LastName}</p>
										</div>
									</div>
									<div>
										<Label className="text-sm font-medium text-gray-500 flex items-center gap-1">
											<Mail className="h-4 w-4" />
											Email
										</Label>
										<p className="text-lg">{profileData.Email}</p>
									</div>
									<div className="grid grid-cols-2 gap-4">
										<div>
											<Label className="text-sm font-medium text-gray-500 flex items-center gap-1">
												<Calendar className="h-4 w-4" />
												Member Since
											</Label>
											<p className="text-sm">
												{profileData.createdAt ? new Date(profileData.createdAt).toLocaleDateString() : 'N/A'}
											</p>
										</div>
										<div>
											<Label className="text-sm font-medium text-gray-500 flex items-center gap-1">
												<Shield className="h-4 w-4" />
												Account Status
											</Label>
											<p className="text-sm">
												{profileData.isRegular ? 'Regular Customer' : 'New Customer'}
											</p>
										</div>
									</div>
									<Button onClick={() => setEditing(true)}>
										Edit Profile
									</Button>
								</div>
							)}
						</>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
