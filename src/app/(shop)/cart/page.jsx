'use client';

import React, { useState, useEffect } from 'react'
import { handleQuantityDecrement, handleQuantityIncrement, RemoveItemFromCart, viewCart } from '../../globals/Cart';
import { IndianRupee, TrashIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { useToast } from '@/hooks/use-toast';
import { ToastAction } from "@/components/ui/toast";
import Image from 'next/image';
import CartQuantityOptions from './components/CartQuantityOptions';
import Link from 'next/link';

function CheckoutPage() {

	const { toast } = useToast();
	const [Cart, setCart] = useState([]);
	const [Subtotal, setSubtotal] = useState(0);
	const [Savings, setSavings] = useState(0);
	const [Quantity, setQuantity] = useState(0);


	useEffect(() => {
		fetchCart();
	}, []);

	const fetchCart = async () => {
		try {
			const cart = await viewCart();
			let total_price = 0, total_mrp = 0, total_savings = 0;
			cart.data.map((cart_item) => {
				total_price += cart_item.Price;
				total_mrp += (cart_item.Product.Mrp * cart_item.Quantity);
			});
			total_savings = total_mrp - total_price;
			setCart(cart.data);
			setQuantity(cart.data.length || 0);
			setSubtotal(total_price.toFixed(2) || 0);
			setSavings(total_savings.toFixed(2) || 0);
		} catch (error) {
			console.log('Error fetching cart:', error);
		}
	}

	// Quantity handlers
	const handleDecrement = async (product_id) => {
		try {
			const cart = await handleQuantityDecrement({ product_id });
			if (cart.success) {
				toast({
					title: 'Removed from Cart',
				});
			}
			else {
				toast({
					title: 'Error Occured',
					description: 'Error decrementing stock quantity.',
					action: <ToastAction> Try Again </ToastAction>
				});
			}
		} catch (error) {
			toast({
				title: 'Error Occured',
				description: 'Error decrementing stock quantity.',
				action: <ToastAction> Try Again </ToastAction>
			});
		} finally {
			await fetchCart();
		}
	};

	// Delete a product from cart
	const removeItemFromCart = async ({ id }) => {
		try {
			const cart = await RemoveItemFromCart(id);
			if (cart.success) {
				toast({
					title: 'Item Removed'
				})
			} else {
				toast({
					title: 'Error Occured',
					description: 'Some Error occured while removing the item.',
					action: <ToastAction
						altText="Try Again"
						onClick={() => removeItemFromCart({ id })}
					>
						Try Again
					</ToastAction>
				})
			}
		} catch (error) {
			console.log('Error fetching cart:', error);
		} finally {
			fetchCart();
		}
	}


	const handleIncrement = async (product_id) => {
		try {
			const cart = await handleQuantityIncrement({ product_id });
			if (cart.success) {
				toast({
					title: 'Added to cart',
				});
			}
			else {
				toast({
					title: 'Error Occured',
					description: 'Error decrementing stock quantity.',
					action: <ToastAction> Try Again </ToastAction>
				});
			}
		} catch (error) {
			toast({
				title: 'Error Occured',
				description: 'Error decrementing stock quantity.',
				action: <ToastAction> Try Again </ToastAction>
			});
		} finally {
			fetchCart();
		}
	};
	return (
		<>
			<section className='p-6'>
				<h1 className='text-2xl text-primary font-semibold pb-4'>Cart Details</h1>
				<div className='bg-primary text-white md:flex md:justify-between p-4 rounded-lg'>
					{/* Left Container */}
					<div className='flex flex-col w-full md:text-lg'>
						<div className='flex gap-3 w-full'>
							<div className='flex gap-1'>
								<h1 className='font-semibold'>Subtotal </h1>
								<h2>({Quantity >= 1 ? `${Quantity} items` : `${Quantity} item`}):</h2>
							</div>
							<span className='flex items-center gap-1 font-semibold'>
								<IndianRupee className='w-4 h-4' /> {Subtotal}
							</span>
						</div>
						{
							Savings > 0 &&
							(
								<p className='flex gap-3 font-semibold'>
									Savings:
									<span className='flex gap-1 items-center'>
										<IndianRupee className='w-4 h-4' /> {Savings}
									</span>
								</p>
							)
						}
					</div>

					{/* Right Container */}
					<div className='w-full h-full flex items-center mt-3 md:mt-0 md:justify-end'>
						<Link href={'/checkout'}>
							<Button variant='outline2' className='h-full'>
								Checkout
							</Button>
						</Link>
					</div>
				</div>
			</section>

			{/* For Medium to Large Screens */}
			<section className='text-gray-700 px-6 md:block hidden'>
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead className='w-[100px]'>Image</TableHead>
							<TableHead>Products ({Quantity > 0 ? `${Quantity} items` : `${Quantity} item`})</TableHead>
							<TableHead>Price</TableHead>
							<TableHead className='w-[100px]'>Quantity</TableHead>
							<TableHead>Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{Cart.map((cart_item, index) => (
							<TableRow key={index}>
								<TableCell>
									<Image
										src={cart_item.Product?.Images?.url || '/placeholder.png'}
										alt={cart_item.Product.Name}
										width={1000}
										height={1000}
										className='rounded-lg'
									/>
								</TableCell>

								<TableCell>
									<h1 className='font-semibold flex items-center gap-2'>
										<span className='py-1 text-lg'>
											{cart_item.Product.Name}
										</span>
										<div className=''>
											<p className='text-xs text-center text-primary border border-primary rounded-full py-1 px-2'>
												{cart_item?.Product?.Category || "Category"}
											</p>
										</div>
									</h1>
									<h2> {cart_item?.Product?.Description || "Description"} </h2>
								</TableCell>

								<TableCell>
									<h1 className='font-semibold'>
										Rs. {cart_item.Price.toFixed(2)}
									</h1>
								</TableCell>

								<TableCell>
									<CartQuantityOptions
										handleIncrement={handleIncrement}
										handleDecrement={handleDecrement}
										product_id={cart_item.Product.id}
										product_qty={cart_item.Quantity}
									/>
								</TableCell>

								<TableCell>
									<TrashIcon
										onClick={() => { removeItemFromCart({ id: cart_item.Product.id }); }}
										className='p-1 text-red-500 bg-red-100 cursor-pointer rounded-lg w-[30px] h-[30px]' />
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</section>

			{/* For Smaller Screens */}
			<section className='text-gray-700 px-6 block md:hidden'>
				{Cart.map((cart_item, index) => (
					<div key={index} className='flex flex-col justify-between items-center p-2 mb-5 border-2 rounded-lg'>
						<div className='flex items-start justify-between w-full'>
							<TrashIcon
								onClick={() => { removeItem({ id: cart_item.Product.id }); }}
								className='p-1 text-red-500 cursor-pointer rounded-lg w-[30px] h-[30px]' />
							<div>
								<h1 className='text-xs text-center text-primary border border-primary rounded-full py-1 px-2'>
									{cart_item?.Product?.Category || "Category"}
								</h1>
							</div>
						</div>
						<Image
							src={cart_item.Product?.Images?.url || '/placeholder.png'}
							alt={cart_item.Product.Name}
							width={1000}
							height={1000}
							className='p-2 h-[120px] object-contain'
						/>
						<div>
							<h1 className='font-semibold'>
								{cart_item.Product.Name}
							</h1>
							<p className='text-xs pb-4'>{cart_item.Product.Description}</p>
							<div className='pb-4'>
								<div className='flex gap-2'>
									<p>Rs. {cart_item.Price.toFixed(2)}</p>
									{cart_item.Product?.SellingPrice && (
										<p className='line-through text-gray-500'>
											Rs. {(cart_item.Product.Mrp * cart_item.Quantity).toFixed(2)}
										</p>
									)}
								</div>
								{cart_item.Product?.SellingPrice && (
									<p className='text-green-800 text-sm'>
										Saved: Rs. {((cart_item.Product.Mrp * cart_item.Quantity) - cart_item.Price).toFixed(2)}
									</p>
								)}
							</div>
							<CartQuantityOptions
								handleIncrement={handleIncrement}
								handleDecrement={handleDecrement}
								product_id={cart_item.Product.id}
								product_qty={cart_item.Quantity}
							/>
						</div>
					</div>
				))}
			</section>
		</>
	)
}

export default CheckoutPage;
