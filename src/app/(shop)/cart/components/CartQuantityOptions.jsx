import React from 'react'

function CartQuantityOptions({ handleIncrement, handleDecrement, product_id, product_qty }) {
	return (
		<div className="p-2 border flex gap-10 items-center">
			<button
				disabled={product_qty === 1}
				onClick={(e) => {
					e.preventDefault();
					handleDecrement(product_id);
				}}
				className="disabled:opacity-50 text-lg  hover:bg-primary hover:text-white py-1 px-2 rounded-lg"
				aria-label="Decrease quantity"
			>
				-
			</button>
			<span className='text-base'>{product_qty}</span>
			<button
				onClick={(e) => {
					e.preventDefault();
					handleIncrement(product_id);
				}}
				aria-label="Increase quantity"
				className="text-lg hover:bg-primary hover:text-white py-1 px-2 rounded-lg"
			>
				+
			</button>
		</div>
	)
}

export default CartQuantityOptions
