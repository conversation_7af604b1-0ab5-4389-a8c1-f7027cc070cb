'use client';
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { User, MapPin, CreditCard, Loader2 } from 'lucide-react';
import { Alert, AlertDescription } from "@/components/ui/alert";

// Step 1: Details Form Component
const DetailsForm = ({ onNext, formData, updateFormData }) => {
	return (
		<div className="space-y-4">
			<div className="space-y-2">
				<label className="text-sm font-medium">Name</label>
				<Input
					value={formData.name || ''}
					onChange={(e) => updateFormData('name', e.target.value)}
					required
				/>
			</div>
			<div className="space-y-2">
				<label className="text-sm font-medium">Email</label>
				<Input
					type="email"
					value={formData.email || ''}
					onChange={(e) => updateFormData('email', e.target.value)}
					required
				/>
			</div>
			<Button
				onClick={onNext}
				disabled={!formData.name || !formData.email}
				className="w-full"
			>
				Next Step
			</Button>
		</div>
	);
};

// Step 2: Address Form Component
const AddressForm = ({ onNext, onBack, formData, updateFormData }) => {
	const [loading, setLoading] = useState(false);
	const [locationError, setLocationError] = useState('');
	const [showManualInput, setShowManualInput] = useState(false);
	const [map, setMap] = useState(null);
	const [marker, setMarker] = useState(null);

	useEffect(() => {
		// Load Google Maps Script
		const script = document.createElement('script');
		script.src = `https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&libraries=places`;
		script.async = true;
		script.onload = initializeMap;
		document.body.appendChild(script);

		return () => {
			document.body.removeChild(script);
		};
	}, []);

	const initializeMap = () => {
		const mapInstance = new window.google.maps.Map(document.getElementById('map'), {
			center: { lat: 0, lng: 0 },
			zoom: 15,
		});

		const markerInstance = new window.google.maps.Marker({
			map: mapInstance,
			draggable: true,
		});

		// Add listener for marker drag end
		markerInstance.addListener('dragend', () => {
			const position = markerInstance.getPosition();
			updateLocationDetails(position.lat(), position.lng());
		});

		setMap(mapInstance);
		setMarker(markerInstance);

		// Initialize the autocomplete
		const autocomplete = new window.google.maps.places.Autocomplete(
			document.getElementById('address-input')
		);

		autocomplete.addListener('place_changed', () => {
			const place = autocomplete.getPlace();
			if (place.geometry) {
				const location = place.geometry.location;
				mapInstance.setCenter(location);
				markerInstance.setPosition(location);
				updateLocationDetails(location.lat(), location.lng(), place);
			}
		});
	};

	const updateLocationDetails = async (lat, lng, placeDetails = null) => {
		try {
			if (!placeDetails) {
				const response = await fetch(
					`https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=YOUR_GOOGLE_MAPS_API_KEY`
				);
				const data = await response.json();
				placeDetails = data.results[0];
			}

			// Create Google Maps URL for the location
			const mapsUrl = `https://www.google.com/maps?q=${lat},${lng}`;

			// Update form data with all location details
			updateFormData('address', placeDetails.formatted_address);
			updateFormData('latitude', lat);
			updateFormData('longitude', lng);
			updateFormData('mapsUrl', mapsUrl);

			// Extract city and postal code from address components
			placeDetails.address_components.forEach(component => {
				if (component.types.includes('locality')) {
					updateFormData('city', component.long_name);
				}
				if (component.types.includes('postal_code')) {
					updateFormData('postalCode', component.long_name);
				}
			});
		} catch (error) {
			setLocationError('Error getting address details');
		}
	};

	const getCurrentLocation = () => {
		setLoading(true);
		setLocationError('');

		if (navigator.geolocation) {
			navigator.geolocation.getCurrentPosition(
				(position) => {
					const { latitude, longitude } = position.coords;

					if (map && marker) {
						const location = new window.google.maps.LatLng(latitude, longitude);
						map.setCenter(location);
						marker.setPosition(location);
						updateLocationDetails(latitude, longitude);
					}

					setLoading(false);
				},
				(error) => {
					setLocationError('Error getting your location. Please enter manually.');
					setShowManualInput(true);
					setLoading(false);
				}
			);
		} else {
			setLocationError('Geolocation is not supported by your browser');
			setShowManualInput(true);
			setLoading(false);
		}
	};

	return (
		<div className="space-y-4">
			<div className="space-y-2">
				{locationError && (
					<Alert variant="destructive">
						<AlertDescription>{locationError}</AlertDescription>
					</Alert>
				)}

				<Button
					onClick={getCurrentLocation}
					disabled={loading}
					className="w-full"
				>
					{loading ? (
						<Loader2 className="w-4 h-4 mr-2 animate-spin" />
					) : (
						<MapPin className="w-4 h-4 mr-2" />
					)}
					{loading ? 'Getting Location...' : 'Use My Current Location'}
				</Button>

				<Button
					onClick={() => setShowManualInput(true)}
					variant="outline"
					className="w-full mt-2"
				>
					Enter Address Manually
				</Button>
			</div>

			<div id="map" className="w-full h-64 rounded-lg border" />

			{(showManualInput || formData.address) && (
				<>
					<div className="space-y-2">
						<label className="text-sm font-medium">Address</label>
						<Input
							id="address-input"
							value={formData.address || ''}
							onChange={(e) => updateFormData('address', e.target.value)}
							placeholder="Start typing your address..."
							required
						/>
					</div>

					<div className="space-y-2">
						<label className="text-sm font-medium">City</label>
						<Input
							value={formData.city || ''}
							onChange={(e) => updateFormData('city', e.target.value)}
							required
						/>
					</div>

					<div className="space-y-2">
						<label className="text-sm font-medium">Postal Code</label>
						<Input
							value={formData.postalCode || ''}
							onChange={(e) => updateFormData('postalCode', e.target.value)}
							required
						/>
					</div>
				</>
			)}

			<div className="flex gap-2">
				<Button onClick={onBack} variant="outline" className="w-full">
					Back
				</Button>
				<Button
					onClick={onNext}
					disabled={!formData.address || !formData.city || !formData.postalCode}
					className="w-full"
				>
					Next Step
				</Button>
			</div>
		</div>
	);
};


// Step 3: Payment Form Component
const PaymentForm = ({ onSubmit, onBack, formData, updateFormData }) => {
	return (
		<div className="space-y-4">
			<div className="space-y-2">
				<label className="text-sm font-medium">Card Number</label>
				<Input
					value={formData.cardNumber || ''}
					onChange={(e) => updateFormData('cardNumber', e.target.value)}
					required
				/>
			</div>
			<div className="space-y-2">
				<label className="text-sm font-medium">Expiry Date</label>
				<Input
					value={formData.expiryDate || ''}
					onChange={(e) => updateFormData('expiryDate', e.target.value)}
					required
				/>
			</div>
			<div className="space-y-2">
				<label className="text-sm font-medium">CVV</label>
				<Input
					value={formData.cvv || ''}
					onChange={(e) => updateFormData('cvv', e.target.value)}
					required
				/>
			</div>
			<div className="flex gap-2">
				<Button onClick={onBack} variant="outline" className="w-full">
					Back
				</Button>
				<Button
					onClick={onSubmit}
					disabled={!formData.cardNumber || !formData.expiryDate || !formData.cvv}
					className="w-full"
				>
					Submit Order
				</Button>
			</div>
		</div>
	);
};

const Progress = () => {
	const [currentStep, setCurrentStep] = useState(0);
	const [formData, setFormData] = useState({});

	const steps = [
		{
			title: 'Details',
			description: 'Where we sending it?',
			icon: User
		},
		{
			title: 'Address',
			description: 'Some info about you.',
			icon: MapPin
		},
		{
			title: 'Payment',
			description: 'Last Step',
			icon: CreditCard
		}
	];

	const updateFormData = (field, value) => {
		setFormData(prev => ({ ...prev, [field]: value }));
	};

	const handleNext = () => {
		setCurrentStep(prev => prev + 1);
	};

	const handleBack = () => {
		setCurrentStep(prev => prev - 1);
	};

	const handleSubmit = async () => {
		try {
			const response = await fetch('/api/order', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(formData),
			});

			if (response.ok) {
				alert('Order submitted successfully!');
			} else {
				throw new Error('Failed to submit order');
			}
		} catch (error) {
			alert('Error submitting order: ' + error.message);
		}
	};

	return (
		<Card className="w-full max-w-2xl mx-auto">
			<CardHeader>
				<CardTitle>Complete Your Order</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="mb-8">
					<div className="flex justify-between">
						{steps.map((step, index) => {
							const StepIcon = step.icon;
							return (
								<div
									key={step.title}
									className={`flex flex-col items-center w-1/3 ${index <= currentStep ? 'text-primary' : 'text-gray-400'
										}`}
								>
									<div className={`
                    w-10 h-10 rounded-full border-2 flex items-center justify-center mb-2
                    ${index <= currentStep ? 'border-primary bg-primary text-white' : 'border-gray-300'}
                  `}>
										<StepIcon className="w-5 h-5" />
									</div>
									<div className="text-sm font-medium">{step.title}</div>
									<div className="text-xs text-gray-500">{step.description}</div>
								</div>
							);
						})}
					</div>
					<div className="relative mt-2">
						<div className="absolute top-0 left-0 h-1 bg-gray-200 w-full">
							<div
								className="h-full bg-primary transition-all duration-300"
								style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
							/>
						</div>
					</div>
				</div>

				{currentStep === 0 && (
					<DetailsForm
						onNext={handleNext}
						formData={formData}
						updateFormData={updateFormData}
					/>
				)}
				{currentStep === 1 && (
					<AddressForm
						onNext={handleNext}
						onBack={handleBack}
						formData={formData}
						updateFormData={updateFormData}
					/>
				)}
				{currentStep === 2 && (
					<PaymentForm
						onSubmit={handleSubmit}
						onBack={handleBack}
						formData={formData}
						updateFormData={updateFormData}
					/>
				)}
			</CardContent>
		</Card>
	);
};

export default Progress;
