'use client';
import Link from 'next/link';
import React, { useEffect, useState } from 'react'
import { Classes } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, LoaderIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { ToastAction } from "@/components/ui/toast";
import { useAuth } from '@/app/contexts/Auth';
import { useRouter } from "next/navigation";

function Login() {
	const { toast } = useToast();
	const router = useRouter();
	const { login, loading, setloading, user } = useAuth();
	const [showPassword, setshowPassword] = useState(false);
	const [formDetails, setFormDetails] = useState({
		email: '',
		password: ''
	});

	async function handleSubmit(e) {
		e.preventDefault();
		setloading(true);
		try {
			const result = await login(formDetails);
			if (result.success) {
				toast({
					title: 'Logged In Successfully!!',
					description: "Welcome Back!! Let's continue shopping, Shall we???",
					action: <Link href='/'><ToastAction altText="Okay"> Yeah!! </ToastAction> </Link>,
				});
			}
			else {
				toast({
					title: 'Something went wrong!',
					description: result.message,
					action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
				});
			}
		} catch (error) {
			toast({
				title: 'Temporary Server Issue',
				description: 'Server is facing some issues, so please try again later',
				action: <Link href='/'>	<ToastAction altText="Okay"> Okay </ToastAction> </Link>
			});
		} finally {
			setloading(false);
		}
	}

	useEffect(() => {
		if (!loading && user) {
			router.push('/')
		}
	}, [user]);


	return (
		<form className="flex flex-col gap-6">
			{/** Input Field with Padding and Borders */}
			<div className="col-span-6">
				<Label htmlFor="FirstName">
					Email
				</Label>

				<input
					type="email"
					value={formDetails.email}
					onChange={(e) => {
						e.preventDefault();
						setFormDetails({ ...formDetails, email: e.target.value });
					}}
					className={Classes}
					placeholder='eg; <EMAIL>'
				/>
			</div>

			<div className="col-span-6 sm:col-span-3">
				<Label htmlFor="Password">
					Password
				</Label>

				<div className='flex gap-2'>
					<input
						type={`${showPassword ? "text" : "password"}`}
						value={formDetails.password}
						onChange={(e) => {
							e.preventDefault();
							setFormDetails({ ...formDetails, password: e.target.value });
						}}
						className={Classes}
						placeholder='******'
					/>
					<Button
						onClick={(e) => { e.preventDefault(); setshowPassword(!showPassword); }}
					>
						{showPassword ? <EyeOff /> : <Eye />}
					</Button>
				</div>

			</div>

			<div className="col-span-6 flex flex-col gap-3">
				<button
					onClick={handleSubmit}
					disabled={!(formDetails.email || formDetails.password)}
					className="inline-block shrink-0 rounded-md border border-blue-600 bg-blue-600 px-12 py-3 text-sm font-medium text-white transition hover:bg-transparent hover:text-blue-600 focus:ring-3 focus:outline-hidden">
					{loading ? (<div className='w-full flex justify-center'><LoaderIcon className='animate-spin text-center' /></div>) : 'Resume Shopping'}
				</button>
			</div>
		</form>
	)
}

export default Login;
