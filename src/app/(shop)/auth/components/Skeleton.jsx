import React from 'react'
import { CompanyName } from '@/app/components/Constants';
import { mascot, white_logo } from '@/app/components/Logo';
import Image from 'next/image';
import Link from 'next/link';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card"
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>Trigger,
} from "@/components/ui/tabs"
import Register from './Register';
import Login from './Login';

function Skeleton() {
	return (

		<section>
			<div className=''>
				<section className="bg-white">
					<div className="lg:grid lg:min-h-screen lg:grid-cols-12">
						<section className="relative flex h-32 items-center bg-gray-900 lg:col-span-5 lg:h-full xl:col-span-6">
							<Image
								alt=""
								src="/login_bg.jpg"
								width={1000}
								height={1000}
								className="absolute inset-0 h-full w-full object-cover opacity-50"
							/>

							{/* For Larger Screens */}
							<div className="hidden lg:relative lg:block lg:px-12">
								<Link className="block text-white" href="/">
									<span className="sr-only">Home</span>
									{white_logo}
								</Link>

								<h2 className="mt-6 text-2xl font-bold text-white sm:text-3xl md:text-4xl">
									Welcome to {CompanyName}
								</h2>

								<p className="mt-4 leading-relaxed text-lg text-white/90">
									Glad to have you here, why don't we start ourselves with an introduction
								</p>
							</div>
						</section>

						<main
							className="flex items-center justify-center px-8 py-8 sm:px-12 lg:col-span-7 lg:px-16 lg:py-12 xl:col-span-6"
						>
							{/* For Smaller Screens */}
							<div className="max-w-xl lg:max-w-3xl">
								<div className="relative -mt-16 block lg:hidden">
									<Link
										className="inline-flex size-16 items-center justify-center rounded-full bg-white text-blue-600 sm:size-20"
										href="/"
									>
										<span className="sr-only">Home</span>
										{mascot}
									</Link>

									<h1 className="mt-2 text-2xl font-bold text-gray-900 sm:text-3xl md:text-4xl">
										Welcome to {CompanyName}
									</h1>

									<p className="mt-4 leading-relaxed text-gray-500">
										Glad to have you here, why don't we start ourselves with an introduction
									</p>
								</div>

								{/* Tabs */}
								<Tabs defaultValue="login" className="w-full lg:w-[40dvw]">
									<TabsList className="grid w-full grid-cols-2">
										<TabsTrigger value="login">Login</TabsTrigger>
										<TabsTrigger value="register">Register</TabsTrigger>
									</TabsList>
									<TabsContent value="login">
										<Card>
											<CardHeader>
												<CardTitle>Sign In</CardTitle>
												<CardDescription>
													Hello, Glad to have you back please Sign-In with your existing details
												</CardDescription>
											</CardHeader>
											<CardContent className="space-y-2">
												<Login />
											</CardContent>
										</Card>
									</TabsContent>
									<TabsContent value="register">
										<Card>
											<CardHeader>
												<CardTitle>Sign Up</CardTitle>
												<CardDescription>
													New to the place? Worry not! Start out with filling out
													your details then enjoy unlimited shopping...
												</CardDescription>
											</CardHeader>
											<CardContent className="space-y-2">
												<Register />
											</CardContent>
										</Card>
									</TabsContent>
								</Tabs>
							</div>
						</main>
					</div>
				</section>

			</div>
		</section>
	)
}

export default Skeleton
