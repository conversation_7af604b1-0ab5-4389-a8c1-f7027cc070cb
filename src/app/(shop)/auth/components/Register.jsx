'use client';
import Link from 'next/link';
import React, { useState } from 'react'
import { Classes } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, LoaderIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { ToastAction } from "@/components/ui/toast"
import { useAuth } from '@/app/contexts/Auth';


function Register() {
	const { toast } = useToast();
	const { register, loading, setloading } = useAuth();
	const [showPassword, setshowPassword] = useState(false);
	const [formDetails, setFormDetails] = useState({
		first_name: '',
		last_name: '',
		email: '',
		password: ''
	});


	async function handleSubmit(e) {
		setloading(true);
		e.preventDefault();
		try {
			const result = await register(formDetails);
			if (result.success) {
				toast({
					title: 'Registered Successfully!!',
					description: 'Glad to have you home...',
					action: <Link href='/'><ToastAction altText="Okay"> Okay </ToastAction> </Link>,
				});
			}
			else {
				toast({
					title: 'Something went wrong!',
					description: result.message,
					action: <ToastAction altText="Try Again"> Try Again </ToastAction>,
				});
			}
		} catch (error) {
			toast({
				title: 'Temporary Server Issue',
				description: 'Server is facing some issues, so please try again later',
				action: <Link href='/'>	<ToastAction altText="Okay"> Okay </ToastAction> </Link>
			});
		} finally {
			setloading(false);
		}
	}

	return (
		<form className="flex flex-col gap-6">
			{/** Input Field with Padding and Borders */}
			<div className="col-span-6 sm:col-span-3">
				<Label htmlFor="FirstName">
					First Name
				</Label>

				<input
					type="text"
					value={formDetails.first_name}
					onChange={(e) => {
						e.preventDefault();
						setFormDetails({ ...formDetails, first_name: e.target.value });
					}}
					className={Classes}
					placeholder='eg; John'
				/>
			</div>

			<div className="col-span-6 sm:col-span-3">
				<Label htmlFor="FirstName">
					Last Name
				</Label>

				<input
					type="text"
					value={formDetails.last_name}
					onChange={(e) => {
						e.preventDefault();
						setFormDetails({ ...formDetails, last_name: e.target.value });
					}}
					className={Classes}
					placeholder='eg; Doe'
				/>
			</div>

			<div className="col-span-6">
				<Label htmlFor="FirstName">
					Email
				</Label>

				<input
					type="email"
					value={formDetails.email}
					onChange={(e) => {
						e.preventDefault();
						setFormDetails({ ...formDetails, email: e.target.value });
					}}
					className={Classes}
					placeholder='eg; <EMAIL>'
				/>
			</div>

			<div className="col-span-6 sm:col-span-3">
				<Label htmlFor="Password">
					Password
				</Label>

				<div className='flex gap-2'>
					<input
						type={`${showPassword ? "text" : "password"}`}
						value={formDetails.password}
						onChange={(e) => {
							e.preventDefault();
							setFormDetails({ ...formDetails, password: e.target.value });
						}}
						className={Classes}
						placeholder='******'
					/>
					<Button onClick={(e) => { e.preventDefault(); setshowPassword(!showPassword); }}>
						{showPassword ? <EyeOff /> : <Eye />}
					</Button>
				</div>

			</div>

			<div className="col-span-6">
				<p className="text-sm text-gray-500">
					By creating an account, you agree to our&nbsp;
					<Link href="#" className="text-gray-700 underline">terms and conditions</Link>
					&nbsp;and&nbsp;
					<Link href="#" className="text-gray-700 underline">privacy policy</Link>.
				</p>
			</div>

			<div className="col-span-6 flex flex-col gap-3">
				<button
					onClick={handleSubmit}
					className="inline-block shrink-0 rounded-md border border-blue-600 bg-blue-600 px-12 py-3 text-sm font-medium text-white transition hover:bg-transparent hover:text-blue-600 focus:ring-3 focus:outline-hidden">
					{loading ? (<div className='w-full flex justify-center'><LoaderIcon className='animate-spin text-center' /></div>) : 'Create an account'}
				</button>
			</div>
		</form>
	)
}

export default Register;
