
import React from 'react'
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import Image from 'next/image';

function LargeScreen({ Quantity, Cart }) {
	return (
		<Table className='mt-4'>
			<TableHeader>
				<TableRow>
					<TableHead className='w-[100px]'>Image</TableHead>
					<TableHead>Products ({Quantity > 0 ? `${Quantity} items` : `${Quantity} item`})</TableHead>
					<TableHead>Quantity</TableHead>
					<TableHead>Price</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{Cart.map((cart_item, index) => (
					<TableRow key={index}>
						<TableCell>
							<Image
								src={cart_item.ProductId?.Images[0]?.url || '/placeholder.png'}
								alt={cart_item.ProductId.Name || 'Product'}
								width={1000}
								height={1000}
								className='rounded-lg'
							/>
						</TableCell>

						<TableCell>
							<h1 className='font-semibold flex items-center gap-2'>
								<span className='py-1 text-lg'>
									{cart_item.ProductId.Name}
								</span>
								<div className=''>
									<p className='text-xs text-center text-primary border border-primary rounded-full py-1 px-2'>
										{cart_item?.ProductId?.Categories.Name || "Category"}
									</p>
								</div>
							</h1>
							<h2> {cart_item?.ProductId?.Description || "Description"} </h2>
						</TableCell>

						<TableCell>
							<h1 className='font-semibold'>
								{cart_item.Quantity}
							</h1>
						</TableCell>

						<TableCell>
							<h1 className='font-semibold'>
								Rs. {cart_item.Price.toFixed(2)}
							</h1>
						</TableCell>
					</TableRow>
				))}
			</TableBody>
		</Table>
	)
}

export default LargeScreen
