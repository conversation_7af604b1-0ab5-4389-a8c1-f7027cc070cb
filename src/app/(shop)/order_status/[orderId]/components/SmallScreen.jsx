import Image from 'next/image'
import React from 'react'

function SmallScreen({ Cart }) {
	return (
		<div className='mt-4'>
			{Cart.map((cart_item, index) => (
				<div key={index} className='flex flex-col justify-between items-center p-2 mb-5 border-2 rounded-lg'>
					<div className='flex items-start justify-end w-full'>
						<div>
							<h1 className='text-xs text-center text-primary border border-primary rounded-full py-1 px-2'>
								{cart_item?.ProductId?.Categories?.Name || "Category"}
							</h1>
						</div>
					</div>
					<Image
						src={cart_item.ProductId?.Images[0]?.url || '/placeholder.png'}
						alt={cart_item.ProductId.Name}
						width={1000}
						height={1000}
						className='p-2 h-[120px] object-contain'
					/>
					<div>
						<h1 className='font-semibold'>
							{cart_item.ProductId.Name}
						</h1>
						<p className='text-xs pb-4'>{cart_item.ProductId.Description}</p>
						<div className='pb-4'>
							<div className='flex gap-2 text-sm'>
								<p>Rs. {cart_item.Price.toFixed(2)}</p>
								{cart_item.ProductId?.SellingPrice && (
									<p className='line-through text-gray-500'>
										Rs. {(cart_item.ProductId.MarketRatePrice * cart_item.Quantity).toFixed(2)}
									</p>
								)}
							</div>
							{cart_item.ProductIdId?.SellingPrice && (
								<p className='text-green-800 text-sm'>
									Saved: Rs. {((cart_item.ProductId.Mrp * cart_item.Quantity) - cart_item.Price).toFixed(2)}
								</p>
							)}
						</div>
					</div>
				</div>
			))}
		</div>
	)
}

export default SmallScreen
