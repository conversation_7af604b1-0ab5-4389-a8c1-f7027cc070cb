'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Check, Truck, Package } from 'lucide-react';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog"
import LargeScreen from './components/LargeScreen';
import { Button } from '@/components/ui/button';
import SmallScreen from './components/SmallScreen';
import { minShippingCost, minShippingRate, tax_rate } from '@/app/globals/Constants';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

const OrderConfirmationPage = ({ params }) => {
	const router = useRouter();
	const resolvedParams = React.use(params);
	const { orderId } = resolvedParams;

	const [loading, setLoading] = useState(true);
	const [orderDetails, setOrderDetails] = useState(null);
	const [error, setError] = useState(null);
	const [Title, setTitle] = useState('');
	const [Description, setDescription] = useState('');
	const [Cart, setCart] = useState([]);
	const [SubTotal, setSubTotal] = useState(0);
	const [ShippingCost, setShippingCost] = useState(0);
	const [Tax, setTax] = useState(0);
	const [TotalPrice, setTotalPrice] = useState(0);
	const [Quantity, setQuantity] = useState(0);

	useEffect(() => {
		const getOrderDetails = async () => {
			if (!orderId) return;

			try {
				setLoading(true);
				const response = await fetch(`/api/orders?order_id=${orderId}`);
				const data = await response.json();
				if (data.returncode === 200) {
					setOrderDetails(data.output);
					setCart(data.output.Cart);
					const subtotal = data.output.Cart?.reduce((total, item) => total + item.Price, 0) || 0;
					const quantity = data.output.Cart.length;
					const shippingCost = subtotal > minShippingCost ? 0 : minShippingRate;
					const tax = subtotal * tax_rate; // 5% tax
					const total = subtotal + shippingCost + tax;
					setSubTotal(subtotal);
					setShippingCost(shippingCost);
					setTax(tax);
					setTotalPrice(total);
					setQuantity(quantity);

					switch (data.output.OrderStatus) {
						case 'Order Placed':
							setTitle('Order is Placed!!');
							setDescription('The order is placed and will be ready for shipping soon...')
							break;
						case 'Order Shipped':
							setTitle('Order is Shipped!!');
							setDescription('The order is shipped and will be at your doorstep any time soon...')
							break;
						case 'Order Delivered':
							setTitle('Order is Delivered!!');
							setDescription('The order is delivered hope it suits to your tastes...')
							break;
						default:
							setTitle('Oops');
							setDescription('Something wrong with order, please try again later or contact customer care...')
							break;
					}
				} else {
					throw new Error('Order not found');
				}
			} catch (error) {
				setError(error.message);
				// Redirect to home page after a short delay
				setTimeout(() => {
					router.push('/');
				}, 3000);
			} finally {
				setLoading(false);
			}
		};

		getOrderDetails();
	}, [orderId, router]);

	if (loading) {
		return (
			<div className="flex justify-center items-center min-h-screen">
				<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-400"></div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex flex-col items-center justify-center min-h-screen">
				<div className="text-xl text-red-500 mb-4">Order not found</div>
				<div className="text-gray-600">Redirecting to home page...</div>
			</div>
		);
	}

	const getStatusNumber = (status) => {
		switch (status) {
			case 'Order Placed': return 1;
			case 'Order Shipped': return 2;
			case 'Order Delivered': return 3;
			default: return 1;
		}
	};

	const currentStatus = orderDetails ? getStatusNumber(orderDetails.OrderStatus) : 1;

	return (
		<div className="max-w-2xl mx-auto p-8 bg-white rounded-lg shadow-sm">
			{/* Confirmation Message */}
			<div className="text-center mb-8">
				<h1 className="text-4xl font-bold mb-4">
					{Title}
				</h1>
				<p className="text-gray-600 mb-2">
					{Description}
				</p>
			</div>

			{/* Status Timeline */}
			<div className="relative mb-12">
				<div className="flex justify-between items-center mb-4">
					{/* Progress Line */}
					<div className="absolute left-0 right-0 h-px bg-gray-300 top-7" />

					{/* Confirmed Status */}
					<div className="relative z-10 flex flex-col items-center">
						<div className={`w-14 h-14 rounded-full flex items-center justify-center ${currentStatus >= 1 ? 'bg-red-400' : 'bg-gray-200'
							}`}>
							<Check className={`w-6 h-6 ${currentStatus >= 1 ? 'text-white' : 'text-gray-400'}`} />
						</div>
						<span className="mt-2 text-sm text-gray-600">Order<br />Placed</span>
					</div>

					{/* Shipped Status */}
					<div className="relative z-10 flex flex-col items-center">
						<div className={`w-14 h-14 rounded-full flex items-center justify-center ${currentStatus >= 2 ? 'bg-red-400' : 'bg-gray-200'
							}`}>
							<Truck className={`w-6 h-6 ${currentStatus >= 2 ? 'text-white' : 'text-gray-400'}`} />
						</div>
						<span className="mt-2 text-sm text-gray-600">Order<br />Shipped</span>
					</div>

					{/* Delivered Status */}
					<div className="relative z-10 flex flex-col items-center">
						<div className={`w-14 h-14 rounded-full flex items-center justify-center ${currentStatus >= 3 ? 'bg-red-400' : 'bg-gray-200'
							}`}>
							<Package className={`w-6 h-6 ${currentStatus >= 3 ? 'text-white' : 'text-gray-400'}`} />
						</div>
						<span className="mt-2 text-sm text-gray-600">Order<br />Delivered</span>
					</div>
				</div>
			</div>

			{/* Order Summary */}
			<Card className="h-[320px] mx-auto">
				<CardHeader>
					<CardTitle>Order Summary</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">

					{/* Totals */}
					<div className="space-y-2">
						<div className="flex justify-between items-center">
							<span className="text-sm">Subtotal</span>
							<span className="font-semibold">₹{SubTotal.toFixed(2)}</span>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm">Shipping</span>
							<span className="font-semibold">₹{ShippingCost.toFixed(2)}</span>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm">Tax ({tax_rate * 100}%)</span>
							<span className="font-semibold">₹{Tax.toFixed(2)}</span>
						</div>
						<div className='border-b w-full rounded-full'></div>
						<div className="flex justify-between items-center">
							<span className="font-semibold">Total</span>
							<span className="font-bold text-lg">₹{TotalPrice.toFixed(2)}</span>
						</div>
					</div>

				</CardContent>
				<CardFooter>
					<Dialog>
						<DialogTrigger asChild>
							<Button className="rounded-lg w-full">
								Order Details
							</Button>
						</DialogTrigger>
						<DialogContent>
							<DialogHeader>
								<DialogTitle>Order Details</DialogTitle>
							</DialogHeader>
							<div>
								{/* For Medium to Large Screens */}
								<section className='text-gray-700 px-6 md:block hidden'>
									<LargeScreen Quantity={Quantity} Cart={Cart} />
								</section>
								{/* For Small Screens */}
								<section className='text-gray-700 px-6 block md:hidden'>
									<SmallScreen Cart={Cart} />
								</section>

							</div>
						</DialogContent>
					</Dialog >
				</CardFooter>
			</Card>





			{/* Footer Text */}
			<p className="text-center text-gray-600 text-sm my-10">
				Reach out to the seller for any order concerns.
				Additional information is accessible.
			</p>
		</div>
	);
};

export default OrderConfirmationPage;
