'use client';
import { useAuth } from '@/app/contexts/Auth';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import Skeleton from './components/Skeleton';

function CheckoutPage() {
	const { user, loading } = useAuth();
	const router = useRouter();
	const [shouldRedirect, setShouldRedirect] = useState(false);

	useEffect(() => {
		let timeoutId;
		if (!loading) {
			timeoutId = setTimeout(() => {
				if (!user) {
					setShouldRedirect(true);
				}
			}, 1000);
		}
		return () => timeoutId && clearTimeout(timeoutId);
	}, [user, loading]);

	useEffect(() => {
		if (shouldRedirect) {
			router.push('/auth');
		}
	}, [shouldRedirect, router]);


	if (loading || !user) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
			</div>
		);
	}

	return (
		<div className='p-6'>
			<Skeleton />
		</div>
	);
}

export default CheckoutPage;
