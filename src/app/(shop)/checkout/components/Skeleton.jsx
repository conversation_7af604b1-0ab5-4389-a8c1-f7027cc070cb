'use client';

import React, { useContext, useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { DetailsForm } from './DetailsForm';
import { AddressForm } from './AddressForm';
import { PaymentForm } from './PaymentForm';
import { CreditCard, MapPin, User } from 'lucide-react';
import { viewCart } from '@/app/globals/Cart';
import { toast } from '@/hooks/use-toast';
import { ToastAction } from '@radix-ui/react-toast';
import { clearCart } from "@/app/globals/Cart";
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { tax_rate, minShippingRate, minShippingCost } from '@/app/globals/Constants';
import { CartContext } from '@/app/contexts/Cart';

function Skeleton() {

	const { setUpdateCart } = useContext(CartContext);
	const router = useRouter();
	const [currentStep, setCurrentStep] = useState(0);
	const [formData, setFormData] = useState({});
	const [Cart, setCart] = useState([]);
	const [SubTotal, setSubTotal] = useState(0);
	const [ShippingCost, setShippingCost] = useState(0);
	const [Tax, setTax] = useState(0);
	const [TotalPrice, setTotalPrice] = useState(0);

	const steps = [
		{
			title: 'Details',
			description: 'Person to Contact.',
			icon: User
		},
		{
			title: 'Address',
			description: 'Where we sending it?',
			icon: MapPin
		},
		{
			title: 'Payment',
			description: 'Last Step',
			icon: CreditCard
		}
	];

	const updateFormData = (field, value) => {
		setFormData(prev => ({ ...prev, [field]: value }));
	};

	const handleNext = () => {
		setCurrentStep(prev => prev + 1);
	};

	const handleBack = () => {
		setCurrentStep(prev => prev - 1);
	};

	const handleSubmit = async () => {
		if (!validateForm()) return;
		try {
			formData.cart = Cart;
			console.log(Cart)
			const response = await fetch('/api/orders', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(formData),
			});

			const data = await response.json();

			if (data.returncode === 200) {
				toast({
					title: 'Successfully placed order'
				});
				await clearCart();
				setUpdateCart(true);
				router.replace(`order_status/${data?.output?._id}`);
			} else {
				toast({
					title: 'Error placing order',
					description: data?.message,
					action: <ToastAction altText='Try Again'> Try Again </ToastAction>
				});
			}
		} catch (error) {
			toast({
				title: 'Error placing order',
				description: error?.message,
				action: <ToastAction altText='Try Again'> Try Again </ToastAction>
			});
		}
	};

	// Validation
	const validateForm = () => {
		const required = ['name', 'contact', 'apartment', 'street_address', 'landmark', 'city', 'pin_code', 'payment_mode'];
		for (const field of required) {
			if (!formData[field]) {
				toast({
					title: "Error",
					description: `Please fill in your ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`,
					variant: "destructive",
				});
				return false;
			}
		}

		// Basic phone validation
		if (!/^\d{10}$/.test(formData.contact)) {
			toast({
				title: "Error",
				description: "Please enter a valid 10-digit phone number",
				variant: "destructive",
			});
			return false;
		}
		return true;
	};


	useEffect(() => {
		async function fetchParams() {
			const cart = await viewCart();
			setCart(cart?.data);
			if (cart?.data?.length > 0) {
				// Cart calculations
				const subtotal = cart.data?.reduce((total, item) => total + item.Price, 0) || 0;
				const shippingCost = subtotal > minShippingCost ? 0 : minShippingRate;
				const tax = subtotal * tax_rate;
				const total = subtotal + shippingCost + tax;
				setSubTotal(subtotal);
				setShippingCost(shippingCost);
				setTax(tax);
				setTotalPrice(total);
			} else {
				toast({
					title: 'Oops',
					description: 'Sorry, got no items in the cart. Are you sure you added something??'
				})
				router.push('/');
			}
		}
		fetchParams();
	}, [])



	return (
		<div className='flex md:flex-row flex-col gap-4'>
			<Card className="w-full max-w-2xl mx-auto">
				<CardHeader>
					<CardTitle>Complete Your Order</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="mb-8">
						<div className="flex justify-between">
							{steps.map((step, index) => {
								const StepIcon = step.icon;
								return (
									<div
										key={step.title}
										className={`flex flex-col items-center w-1/3 ${index <= currentStep ? 'text-primary' : 'text-gray-400'
											}`}
									>
										<div className={`
                    w-10 h-10 rounded-full border-2 flex items-center justify-center mb-2
                    ${index <= currentStep ? 'border-primary bg-primary text-white' : 'border-gray-300'}
                  `}>
											<StepIcon className="w-5 h-5" />
										</div>
										<div className="text-xs font-medium">{step.title}</div>
										<div className="hidden sm:block text-xs text-gray-500">{step.description}</div>
									</div>
								);
							})}
						</div>
						<div className="relative mt-2">
							<div className="absolute top-0 left-0 h-1 bg-gray-200 w-full">
								<div
									className="h-full bg-primary transition-all duration-300"
									style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
								/>
							</div>
						</div>
					</div>

					{currentStep === 0 && (
						<DetailsForm
							onNext={handleNext}
							formData={formData}
							updateFormData={updateFormData}
						/>
					)}
					{currentStep === 1 && (
						<AddressForm
							onNext={handleNext}
							onBack={handleBack}
							formData={formData}
							updateFormData={updateFormData}
						/>
					)}
					{currentStep === 2 && (
						<PaymentForm
							onSubmit={handleSubmit}
							onBack={handleBack}
							formData={formData}
							updateFormData={updateFormData}
						/>
					)}
				</CardContent>
				<CardFooter>
					<p className='text-gray-500 italic text-xs'>
						* In case of online payment once you completed the transaction. Only then the order will be visible on your orders dashboard.
					</p>
				</CardFooter>
			</Card>

			{/* Order Summary */}
			<Card className="max-w-[300px] h-[320px] mx-auto">
				<CardHeader>
					<CardTitle>Order Summary</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">

					{/* Totals */}
					<div className="space-y-2">
						<div className="flex justify-between items-center">
							<span className="text-sm">Subtotal</span>
							<span className="font-semibold">₹{SubTotal.toFixed(2)}</span>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm">Shipping</span>
							<span className="font-semibold">₹{ShippingCost.toFixed(2)}</span>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm">Tax ({tax_rate * 100}%)</span>
							<span className="font-semibold">₹{Tax.toFixed(2)}</span>
						</div>
						<div className='border-b w-full rounded-full'></div>
						<div className="flex justify-between items-center">
							<span className="font-semibold">Total</span>
							<span className="font-bold text-lg">₹{TotalPrice.toFixed(2)}</span>
						</div>
					</div>

				</CardContent>
				<CardFooter>
					<p className="text-sm text-gray-500 text-center mt-4">
						By placing your order, you agree to our
						<Link href="#" className="text-teal-600 hover:underline"> Terms & Conditions </Link>
						and
						<Link href="#" className="text-teal-600 hover:underline"> Privacy Policy</Link>
					</p>
				</CardFooter>
			</Card>
		</div>
	)
}

export default Skeleton
