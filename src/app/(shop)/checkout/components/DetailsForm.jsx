import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export const DetailsForm = ({ onNext, formData, updateFormData }) => {
	return (
		<div className="space-y-4">
			<div className="space-y-2">
				<Label htmlFor=''>Name</Label>
				<Input
					value={formData.name || ''}
					onChange={(e) => updateFormData('name', e.target.value)}
					placeholder='eg; <PERSON>'
					required
				/>
			</div>
			<div className="space-y-2">
				<Label htmlFor=''>Contact</Label>
				<Input
					type='tel'
					value={formData.contact || ''}
					onChange={(e) => updateFormData('contact', e.target.value)}
					placeholder='eg; 1234567890'
					pattern="[0-9]{3}-[0-9]{2}-[0-9]{3}"
					required
				/>
			</div>
			<Button
				onClick={onNext}
				disabled={!formData.name || !formData.contact}
				className="w-full"
			>
				Next Step
			</Button>
		</div>
	);
};
