'use client';
import React, { useState, useContext, useEffect } from 'react'
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { CartContext } from '@/app/contexts/Cart';
import { useContext } from 'react';
import { useToast } from '@/hooks/use-toast';

function Sample() {

	const { cart, updateCart, setUpdateCart } = useContext(CartContext);
	const { toast } = useToast();

	// Form state
	const [formData, setFormData] = useState({
		firstName: '',
		lastName: '',
		apartment: '',
		landmark: '',
		street_address: '',
		city: '',
		postalCode: '',
		phone: '',
		paymentMethod: 'cod'
	});
	const [isSubmitting, setIsSubmitting] = useState(false);


	// Cart calculations
	const subtotal = cart?.reduce((total, item) => total + (item.Price * item.Quantity), 0) || 0;
	const shippingCost = subtotal > 1000 ? 0 : 49; // Free shipping over ₹1000
	const tax = subtotal * 0.05; // 5% tax
	const total = subtotal + shippingCost + tax;

	// Form handling
	const handleInputChange = (e) => {
		const { name, value } = e.target;
		setFormData(prev => ({
			...prev,
			[name]: value
		}));
	};

	const handlePaymentMethodChange = (value) => {
		setFormData(prev => ({
			...prev,
			paymentMethod: value
		}));
	};

	// Validation
	const validateForm = () => {
		const required = ['firstName', 'lastName', 'city', 'postalCode', 'phone'];
		for (const field of required) {
			if (!formData[field]) {
				toast({
					title: "Error",
					description: `Please fill in your ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`,
					variant: "destructive",
				});
				return false;
			}
		}
		// Basic phone validation
		if (!/^\d{10}$/.test(formData.phone)) {
			toast({
				title: "Error",
				description: "Please enter a valid 10-digit phone number",
				variant: "destructive",
			});
			return false;
		}
		return true;
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		if (!validateForm()) return;

		setIsSubmitting(true);
		try {
			// Example order structure
			const orderData = {
				userId: user.id,
				items: cart,
				shippingAddress: {
					firstName: formData.firstName,
					lastName: formData.lastName,
					apartment: formData.apartment,
					street_address: formData.street_address,
					landmark: formData.landmark,
					city: formData.city,
					postalCode: formData.postalCode,
					phone: formData.phone
				},
				paymentMethod: formData.paymentMethod,
				totals: {
					subtotal,
					shipping: shippingCost,
					tax,
					total
				}
			};

			// Replace with your actual API endpoint
			const response = await fetch('/api/orders', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(orderData)
			});

			if (!response.ok) throw new Error('Failed to place order');

			toast({
				title: "Success!",
				description: "Your order has been placed successfully",
			});

			// Clear cart and redirect to order confirmation
			setUpdateCart(!updateCart);
			router.push('/order-confirmation');

		} catch (error) {
			toast({
				title: "Error",
				description: "Failed to place your order. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	};


	return (
		<form onSubmit={handleSubmit} className="container mx-auto px-4 py-8 max-w-6xl">
			<h1 className="text-3xl font-bold mb-8">Checkout</h1>

			<div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
				{/* Main Checkout Form */}
				<div className="lg:col-span-2 space-y-8">
					{/* Delivery Address */}
					<Card>
						<CardHeader>
							<CardTitle>Delivery Address</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-2 gap-4">
								<div className="space-y-2">
									<Label htmlFor="firstName">First Name</Label>
									<Input
										id="firstName"
										name="firstName"
										value={formData.firstName}
										onChange={handleInputChange}
										placeholder="John"
										required
									/>
								</div>
								<div className="space-y-2">
									<Label htmlFor="lastName">Last Name</Label>
									<Input
										id="lastName"
										name="lastName"
										value={formData.lastName}
										onChange={handleInputChange}
										placeholder="Doe"
										required
									/>
								</div>
							</div>
							<div className="space-y-2">
								<Label htmlFor="address">Apartment</Label>
								<Input
									name="apartment"
									value={formData.apartment}
									onChange={handleInputChange}
									placeholder="123 Main St"
									required
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="address">Street Address</Label>
								<Input
									name="street_address"
									value={formData.street_address}
									onChange={handleInputChange}
									placeholder="Spreef Street"
									required
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="address">Landmark</Label>
								<Input
									name="landmark"
									value={formData.landmark}
									onChange={handleInputChange}
									placeholder="Near, Liberty Statue"
									required
								/>
							</div>
							<div className="grid grid-cols-2 gap-4">
								<div className="space-y-2">
									<Label htmlFor="city">City</Label>
									<Input
										id="city"
										name="city"
										value={formData.city}
										onChange={handleInputChange}
										placeholder="City"
										required
									/>
								</div>
								<div className="space-y-2">
									<Label htmlFor="postalCode">Postal Code</Label>
									<Input
										id="postalCode"
										name="postalCode"
										value={formData.postalCode}
										onChange={handleInputChange}
										placeholder="12345"
										required
									/>
								</div>
							</div>
							<div className="space-y-2">
								<Label htmlFor="phone">Phone Number</Label>
								<Input
									id="phone"
									name="phone"
									value={formData.phone}
									onChange={handleInputChange}
									placeholder="Your phone number"
									type="tel"
									required
								/>
							</div>
						</CardContent>
					</Card>

					{/* Payment Method */}
					<Card>
						<CardHeader>
							<CardTitle>Payment Method</CardTitle>
						</CardHeader>
						<CardContent>
							<RadioGroup
								value={formData.paymentMethod}
								onValueChange={handlePaymentMethodChange}
								className="space-y-4"
							>
								<div className="flex items-center space-x-2 border rounded-lg p-4">
									<RadioGroupItem value="cod" id="cod" />
									<Label htmlFor="cod" className="flex-1">Cash on Delivery</Label>
								</div>
								<div className="flex items-center space-x-2 border rounded-lg p-4">
									<RadioGroupItem value="online" id="online" />
									<Label htmlFor="online" className="flex-1">Online Payment</Label>
								</div>
							</RadioGroup>
						</CardContent>
					</Card>
				</div>

				{/* Order Summary */}
				<div className="lg:col-span-1">
					<Card>
						<CardHeader>
							<CardTitle>Order Summary</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							{/* Cart Items */}
							<div className="space-y-3">
								{cart?.map((item) => (
									<div key={item.Product.id} className="flex justify-between text-sm">
										<span>{item.Product.Name} × {item.Quantity}</span>
										<span>₹{(item.Price * item.Quantity).toFixed(2)}</span>
									</div>
								))}
							</div>

							<Separator />

							{/* Totals */}
							<div className="space-y-2">
								<div className="flex justify-between items-center">
									<span className="text-sm">Subtotal</span>
									<span className="font-semibold">₹{subtotal.toFixed(2)}</span>
								</div>
								<div className="flex justify-between items-center">
									<span className="text-sm">Shipping</span>
									<span className="font-semibold">₹{shippingCost.toFixed(2)}</span>
								</div>
								<div className="flex justify-between items-center">
									<span className="text-sm">Tax (5%)</span>
									<span className="font-semibold">₹{tax.toFixed(2)}</span>
								</div>
								<Separator />
								<div className="flex justify-between items-center">
									<span className="font-semibold">Total</span>
									<span className="font-bold text-lg">₹{total.toFixed(2)}</span>
								</div>
							</div>

							<Button
								type="submit"
								className="w-full bg-teal-600 hover:bg-teal-700"
								disabled={isSubmitting}
							>
								{isSubmitting ? 'Processing...' : 'Place Order'}
							</Button>

							<p className="text-sm text-gray-500 text-center mt-4">
								By placing your order, you agree to our
								<a href="#" className="text-teal-600 hover:underline"> Terms of Service </a>
								and
								<a href="#" className="text-teal-600 hover:underline"> Privacy Policy</a>
							</p>
						</CardContent>
					</Card>
				</div>
			</div>
		</form>
	)
}

export default Sample
