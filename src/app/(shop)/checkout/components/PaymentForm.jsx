import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

export const PaymentForm = ({ onSubmit, onBack, formData, updateFormData }) => {
	return (
		<div className="space-y-4">
			<Card>
				<CardHeader>
					<CardTitle>Payment Method</CardTitle>
				</CardHeader>
				<CardContent>
					<RadioGroup
						value={formData.payment_mode || ''}
						onValueChange={(value) => updateFormData('payment_mode', value)}
						className="space-y-4 cursor-pointer"
					>
						<div className="flex items-center space-x-2 border rounded-lg p-4">
							<RadioGroupItem value="Cash on Delivery" id="Cash on Delivery" />
							<Label htmlFor="cod" className="flex-1">Cash on Delivery</Label>
						</div>
						<div className="flex items-center space-x-2 border rounded-lg p-4">
							<RadioGroupItem value="Online Payment" id="Online Payment" />
							<Label htmlFor="online" className="flex-1">Online Payment</Label>
						</div>
					</RadioGroup>
				</CardContent>
			</Card>
			<div className="flex gap-2">
				<Button onClick={onBack} variant="outline" className="w-full">
					Back
				</Button>
				<Button
					onClick={onSubmit}
					disabled={!formData.payment_mode}
					className="w-full"
				>
					Proceed
				</Button>
			</div>
		</div>
	);
};
