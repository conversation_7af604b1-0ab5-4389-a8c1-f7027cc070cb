import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export const AddressForm = ({ onNext, onBack, formData, updateFormData }) => {
	return (
		<div className="space-y-4">
			<div className="space-y-2">
				<Label>Apartment</Label>
				<Input
					value={formData.apartment || ''}
					onChange={(e) => updateFormData('apartment', e.target.value)}
					placeholder='eg; 72/b, Trikam House'
					required
				/>
			</div>
			<div className="space-y-2">
				<Label>Street Address</Label>
				<Input
					value={formData.street_address || ''}
					onChange={(e) => updateFormData('street_address', e.target.value)}
					placeholder='eg; J S S Rd, Nana Chowk'
					required
				/>
			</div>
			<div className="space-y-2">
				<Label>Landmark</Label>
				<Input
					value={formData.landmark || ''}
					onChange={(e) => updateFormData('landmark', e.target.value)}
					placeholder='eg; Near Kala Chowki'
					required
				/>
			</div>
			<div className="space-y-2">
				<Label>City</Label>
				<Input
					value={formData.city || ''}
					onChange={(e) => updateFormData('city', e.target.value)}
					placeholder='eg; Mumbai'
					required
				/>
			</div>
			<div className="space-y-2">
				<Label>Postal Code (Pin Code)</Label>
				<Input
					value={formData.pin_code || ''}
					minLength={6}
					maxLength={6}
					onChange={(e) => updateFormData('pin_code', e.target.value)}
					placeholder='eg; 12345'
					required
				/>
			</div>
			<div className="flex gap-2">
				<Button onClick={onBack} variant="outline" className="w-full">
					Back
				</Button>
				<Button
					onClick={onNext}
					disabled={!formData.apartment || !formData.street_address || !formData.landmark || !formData.city || !formData.pin_code}
					className="w-full"
				>
					Next Step
				</Button>
			</div>
		</div>
	);
};
