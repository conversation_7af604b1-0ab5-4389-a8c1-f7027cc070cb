'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/app/contexts/Auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { ToastAction } from '@/components/ui/toast';
import { Loader2, Package, Calendar, MapPin, CreditCard, Truck, Eye } from 'lucide-react';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";

export default function OrdersPage() {
	const { user } = useAuth();
	const [orders, setOrders] = useState([]);
	const [loading, setLoading] = useState(false);
	const [selectedOrder, setSelectedOrder] = useState(null);

	useEffect(() => {
		if (user) {
			fetchOrders();
		}
	}, [user]);

	const fetchOrders = async () => {
		try {
			setLoading(true);
			const response = await fetch('/api/orders');
			const data = await response.json();
			
			if (data.returncode === 200) {
				setOrders(data.output || []);
			} else {
				toast({
					title: 'Error',
					description: data.message || 'Failed to fetch orders',
					action: <ToastAction altText="Try Again">Try Again</ToastAction>,
				});
			}
		} catch (error) {
			toast({
				title: 'Error',
				description: 'Failed to fetch orders',
				action: <ToastAction altText="Try Again">Try Again</ToastAction>,
			});
		} finally {
			setLoading(false);
		}
	};

	const getStatusColor = (status) => {
		switch (status?.toLowerCase()) {
			case 'order placed':
				return 'bg-blue-100 text-blue-800';
			case 'processing':
				return 'bg-yellow-100 text-yellow-800';
			case 'shipped':
				return 'bg-purple-100 text-purple-800';
			case 'delivered':
				return 'bg-green-100 text-green-800';
			case 'cancelled':
				return 'bg-red-100 text-red-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	const getPaymentStatusColor = (status) => {
		switch (status?.toLowerCase()) {
			case 'paid':
				return 'bg-green-100 text-green-800';
			case 'pending':
				return 'bg-yellow-100 text-yellow-800';
			case 'failed':
				return 'bg-red-100 text-red-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	};

	const calculateOrderTotal = (cart) => {
		return cart?.reduce((total, item) => total + (item.Price || 0), 0) || 0;
	};

	const calculateOrderSavings = (cart) => {
		return cart?.reduce((total, item) => total + (item.Savings || 0), 0) || 0;
	};

	if (!user) {
		return (
			<div className="container mx-auto px-4 py-8">
				<Card>
					<CardContent className="flex items-center justify-center py-8">
						<p>Please log in to view your orders.</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="container mx-auto px-4 py-8">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Package className="h-5 w-5" />
						My Orders
					</CardTitle>
					<CardDescription>
						Track and manage your order history
					</CardDescription>
				</CardHeader>
				<CardContent>
					{loading ? (
						<div className="flex items-center justify-center py-8">
							<Loader2 className="h-8 w-8 animate-spin" />
						</div>
					) : orders.length === 0 ? (
						<div className="text-center py-8">
							<Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
							<p className="text-gray-500">No orders found</p>
							<p className="text-sm text-gray-400">Your order history will appear here</p>
						</div>
					) : (
						<div className="space-y-4">
							{orders.map((order) => (
								<Card key={order._id} className="border-l-4 border-l-primary">
									<CardContent className="p-6">
										<div className="flex justify-between items-start mb-4">
											<div>
												<h3 className="font-semibold text-lg">Order #{order._id.slice(-8)}</h3>
												<p className="text-sm text-gray-500 flex items-center gap-1">
													<Calendar className="h-4 w-4" />
													{new Date(order.createdAt).toLocaleDateString('en-US', {
														year: 'numeric',
														month: 'long',
														day: 'numeric'
													})}
												</p>
											</div>
											<div className="text-right">
												<p className="font-semibold text-lg">₹{calculateOrderTotal(order.Cart)}</p>
												{calculateOrderSavings(order.Cart) > 0 && (
													<p className="text-sm text-green-600">
														Saved ₹{calculateOrderSavings(order.Cart)}
													</p>
												)}
											</div>
										</div>

										<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
											<div>
												<Badge className={getStatusColor(order.OrderStatus)}>
													<Truck className="h-3 w-3 mr-1" />
													{order.OrderStatus}
												</Badge>
											</div>
											<div>
												<Badge className={getPaymentStatusColor(order.PaymentStatus)}>
													<CreditCard className="h-3 w-3 mr-1" />
													{order.PaymentStatus}
												</Badge>
											</div>
											<div>
												<span className="text-sm text-gray-600">
													{order.PaymentMode}
												</span>
											</div>
										</div>

										<div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
											<MapPin className="h-4 w-4" />
											<span>
												{order.Apartment}, {order.StreetAddress}, {order.City} - {order.Pincode}
											</span>
										</div>

										<div className="flex justify-between items-center">
											<div className="text-sm text-gray-600">
												{order.Cart?.length || 0} item(s)
											</div>
											<Dialog>
												<DialogTrigger asChild>
													<Button 
														variant="outline" 
														size="sm"
														onClick={() => setSelectedOrder(order)}
													>
														<Eye className="h-4 w-4 mr-2" />
														View Details
													</Button>
												</DialogTrigger>
												<DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
													<DialogHeader>
														<DialogTitle>Order Details</DialogTitle>
														<DialogDescription>
															Order #{selectedOrder?._id.slice(-8)}
														</DialogDescription>
													</DialogHeader>
													{selectedOrder && (
														<div className="space-y-6">
															<div className="grid grid-cols-2 gap-4">
																<div>
																	<h4 className="font-semibold mb-2">Order Information</h4>
																	<div className="space-y-1 text-sm">
																		<p><span className="font-medium">Order Date:</span> {new Date(selectedOrder.createdAt).toLocaleDateString()}</p>
																		<p><span className="font-medium">Status:</span> {selectedOrder.OrderStatus}</p>
																		<p><span className="font-medium">Payment:</span> {selectedOrder.PaymentStatus}</p>
																		<p><span className="font-medium">Payment Mode:</span> {selectedOrder.PaymentMode}</p>
																	</div>
																</div>
																<div>
																	<h4 className="font-semibold mb-2">Delivery Address</h4>
																	<div className="text-sm">
																		<p className="font-medium">{selectedOrder.Name}</p>
																		<p>{selectedOrder.Contact}</p>
																		<p>{selectedOrder.Apartment}</p>
																		<p>{selectedOrder.StreetAddress}</p>
																		<p>{selectedOrder.Landmark}</p>
																		<p>{selectedOrder.City} - {selectedOrder.Pincode}</p>
																	</div>
																</div>
															</div>
															
															<div>
																<h4 className="font-semibold mb-2">Order Items</h4>
																<div className="space-y-2">
																	{selectedOrder.Cart?.map((item, index) => (
																		<div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
																			<div>
																				<p className="font-medium">Product ID: {item.ProductId}</p>
																				<p className="text-sm text-gray-600">Quantity: {item.Quantity}</p>
																			</div>
																			<div className="text-right">
																				<p className="font-semibold">₹{item.Price}</p>
																				{item.Savings > 0 && (
																					<p className="text-sm text-green-600">Saved ₹{item.Savings}</p>
																				)}
																			</div>
																		</div>
																	))}
																</div>
															</div>

															<div className="border-t pt-4">
																<div className="flex justify-between items-center">
																	<span className="font-semibold">Total Amount:</span>
																	<span className="font-bold text-lg">₹{calculateOrderTotal(selectedOrder.Cart)}</span>
																</div>
																{calculateOrderSavings(selectedOrder.Cart) > 0 && (
																	<div className="flex justify-between items-center text-green-600">
																		<span>Total Savings:</span>
																		<span>₹{calculateOrderSavings(selectedOrder.Cart)}</span>
																	</div>
																)}
															</div>
														</div>
													)}
												</DialogContent>
											</Dialog>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
