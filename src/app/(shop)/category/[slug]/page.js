'use client';
import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react'
import ProductListFilter from '@/app/components/ProductListFilter';
import CategoryList from '@/app/components/CategoryList';

function page() {
	const params = useParams();
	const slug = params?.slug || [];
	const [productsArray, setProductsArray] = useState([]);
	const [title, setTitle] = useState('');
	const [products, setProducts] = useState([]);
	const [loading, setLoading] = useState(true);

	// Fetch products when component mounts or slug changes
	useEffect(() => {
		fetchProducts();
	}, [slug]);

	// Filter products whenever products array or slug changes
	useEffect(() => {
		if (products.length > 0 && slug) {
			filterProducts();
		}
	}, [products, slug]);

	const fetchProducts = async () => {
		try {
			const response = await fetch('/api/admin/products');
			const data = await response.json();
			if (response.ok && data.output.length > 0) {
				setProducts(data.output);
			} else {
				setProducts([]);
				setTitle('');
			}
		} catch (error) {
			console.error('Error fetching Products:', error);
			setProducts([]);
			setTitle('');
		}
	};

	const filterProducts = () => {
		const filtered = products.filter((product) => {
			if (product.Categories.Slug === slug) {
				setTitle(product.Categories.Name);
				return true;
			}
			return false;
		});
		setProductsArray(filtered);

		// If no products found, reset title
		if (filtered.length === 0) {
			setTitle('');
		}

		setLoading(false);
	};

	return (
		<div>
			<h1 className='p-4 bg-primary text-white font-semibold text-3xl text-center'>
				{title}
			</h1>
			<div className="p-5 md:p-10">
				<CategoryList selected={title} />
				{
					loading ? (
						<ProductListFilter Products={productsArray} display_skeleton={true} />
					) : (
						<ProductListFilter Products={productsArray} display_skeleton={false} />
					)
				}
			</div>
		</div>
	)
}

export default page;
