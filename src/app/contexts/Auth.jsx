'use client';

import { createContext, useContext, useState, useEffect } from "react";
import { useRouter } from "next/navigation";

const AuthContext = createContext();

export function AuthContextProvider({ children }) {
	const [user, setuser] = useState(null);
	const [admin, setAdmin] = useState(null);
	const [Cart, setCart] = useState(null);
	const [loading, setloading] = useState(false);
	const router = useRouter();

	useEffect(() => {
		checkAuth();
		checkAdminAuth();
	}, []);

	const checkAuth = async () => {
		try {
			const response = await fetch('/api/users/auth/check');
			const data = await response.json();
			if (data.returncode === 200 && data.output) {
				setuser(data.output[0]);
			} else {
				setuser(null);
			}
		} catch (error) {
			setuser(null);
		} finally {
			setloading(false);
		}
	}

	const checkAdminAuth = async () => {
		try {
			setloading(true);
			const response = await fetch('/api/admin/auth/check');
			const data = await response.json();

			if (data.returncode === 200 && data.output) {
				setAdmin(data.output[0]);
			} else {
				setAdmin(null);
			}
		} catch (error) {
			console.error('Auth check error:', error);
			setAdmin(null);
		} finally {
			setloading(false);
		}
	};


	const login = async (credentials) => {
		try {
			const response = await fetch('/api/users/auth/login', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(credentials)
			});
			const data = await response.json();
			if (data.returncode === 200) {
				setuser(data.output[0]);
				await checkAuth();
				router.push('/');
				return { success: true }
			}
			return {
				success: false,
				message: data.message || 'Login Failed'
			}
		} catch (error) {
			return {
				success: false,
				message: 'An error occurred during login'
			};
		}
	}

	const adminLogin = async (credentials) => {
		try {
			setloading(true);
			const response = await fetch('/api/admin/auth/login', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(credentials)
			});

			const data = await response.json();

			if (data.returncode === 200) {
				setAdmin(data.output[0]);
				router.push('/admin/product_categories');
				return { success: true };
			}

			return {
				success: false,
				message: data.message || 'Login Failed'
			};
		} catch (error) {
			setAdmin(null);
			return {
				success: false,
				message: 'An error occurred during login'
			};
		} finally {
			setloading(false);
		}
	};


	const register = async (credentials) => {
		try {
			const response = await fetch('/api/users/auth/register', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(credentials)
			});
			const data = await response.json();
			if (data.returncode === 200) {
				setuser(data.output[0]);
				await checkAuth();
				router.push('/');
				return { success: true }
			}
			return {
				success: false,
				message: data.message || 'Registration Failed'
			}
		} catch (error) {
			return {
				success: false,
				message: 'An error occurred during registration.'
			};
		}
	}

	const logout = async () => {
		try {
			await fetch('/api/users/auth/logout', { method: 'POST' });
			setuser(null);
			router.push('/auth');
		} catch (error) {
			console.log('Logout error:', error);
		}
	};

	const adminLogout = async () => {
		try {
			setloading(true);
			await fetch('/api/admin/auth/logout', { method: 'POST' });
			setAdmin(null);
			router.push('/admin/auth');
		} catch (error) {
			console.error('Logout error:', error);
		} finally {
			setloading(false);
		}
	};

	return (
		<AuthContext.Provider value={{
			user,
			admin,
			loading,
			setloading,
			login,
			adminLogin,
			register,
			logout,
			adminLogout,
			checkAuth,
			checkAdminAuth,
			Cart,
			setCart
		}}>
			{children}
		</AuthContext.Provider>
	);
}

export function useAuth() {
	const context = useContext(AuthContext);
	if (!context) {
		throw new Error('Error occured using AuthProvider');
	}
	return context;
}
