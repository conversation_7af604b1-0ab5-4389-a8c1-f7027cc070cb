export const addToCart = async ({ data }) => {
  try {
    const Cart = localStorage.getItem('Cart');
    const prevCart = await JSON.parse(Cart);

    // Update Cart
    if (prevCart?.data?.length > 0) {
      let flag = false;
      await prevCart.data.map((cart_item) => {
        // If item already exists (Updating Quantity and Price)
        if (data.Product.id === cart_item.Product.id) {
          cart_item.Quantity += data.Quantity;
          cart_item.Price += data.Price;
          flag = true;
        }
      });

      // If item already doesn't exists (Add the new element to Cart array)
      if (!flag) {
        await prevCart.data.push(data);
      }
      localStorage.setItem('Cart', JSON.stringify(prevCart));
      return { success: true };
    }

    // Add a new cart
    localStorage.setItem('Cart', JSON.stringify({ data: [data] }))
    return { success: true };
  } catch (error) {
    console.log('Failed to add to cart.', error.message);
  }
}

export const viewCart = async () => {
  try {
    const Cart = localStorage.getItem('Cart');
    return JSON.parse(Cart);
  } catch (error) {
    console.log('Failed to fetch the cart.', error.message);
  }
}

export const handleQuantityDecrement = async ({ product_id }) => {
  try {
    const Cart = localStorage.getItem('Cart');
    const prevCart = await JSON.parse(Cart);
    prevCart.data.map((cart_item) => {
      if (product_id === cart_item.Product.id) {
        cart_item.Quantity = cart_item.Quantity - 1;
        cart_item.Price = (
          cart_item.Product?.SellingPrice ? (
            cart_item.Price - cart_item.Product?.SellingPrice
          ) : (
            cart_item.Price - cart_item.Product?.Mrp
          ));
      }
    });
    localStorage.setItem('Cart', JSON.stringify(prevCart));
    return { success: true };
  } catch (error) {
    console.log('Failed to fetch the cart.', error.message);
    return { success: false };
  }
}

export const handleQuantityIncrement = async ({ product_id }) => {
  try {
    const Cart = localStorage.getItem('Cart');
    const prevCart = await JSON.parse(Cart);
    prevCart.data.map((cart_item) => {
      if (product_id === cart_item.Product.id) {
        cart_item.Quantity = cart_item.Quantity + 1;
        cart_item.Price = (
          cart_item.Product?.SellingPrice ? (
            cart_item.Price + cart_item.Product?.SellingPrice
          ) : (
            cart_item.Price + cart_item.Product?.Mrp
          ));
      }
    });
    localStorage.setItem('Cart', JSON.stringify(prevCart));
    return { success: true };
  } catch (error) {
    console.log('Failed to fetch the cart.', error.message);
    return { success: false };
  }
}

export const clearCart = async () => {
  try {
    const Cart = localStorage.removeItem('Cart');
    return Cart;
  } catch (error) {
    console.log('Failed to fetch the cart.', error.message);
  }
}

export const RemoveItemFromCart = async (product_id) => {
  console.log(product_id);
  try {
    const Cart = localStorage.getItem('Cart');
    const prevCart = await JSON.parse(Cart);
    await prevCart.data.map((cart_item, index) => {
      if (product_id === cart_item.Product.id) {
        prevCart.data.splice(index, 1);
      }
    });
    localStorage.setItem('Cart', JSON.stringify(prevCart));
    return { success: true }

  } catch (error) {
    console.log('Failed to fetch the cart.', error.message);
  }
}
