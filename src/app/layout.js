import { Outfit } from "next/font/google";
import "./globals.css";
import RootSkeleton from "./components/RootSkeleton";

const font = Outfit({ subsets: ["latin"], });

export const metadata = {
  title: "Grocery E-Commerce",
  description: "Grocery E-Commerce Store",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${font.className} antialiased`}
      >
        <RootSkeleton children={children} />
      </body>
    </html>
  );
}
