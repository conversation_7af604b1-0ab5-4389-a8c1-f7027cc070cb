import adminsCrud from "@/app/lib/crud/Admins";

export async function get_admin_profile(admin_id) {
	try {
		if (!admin_id) {
			return {
				returncode: 400,
				message: 'Admin ID is required.',
				output: []
			};
		}

		return await adminsCrud.getAdminProfile({ admin_id });
	} catch (error) {
		return {
			returncode: 500,
			message: error.message,
			output: []
		};
	}
}

export async function update_admin_profile(admin_id, data) {
	try {
		const first_name = data['first_name'] || null;
		const last_name = data['last_name'] || null;
		const email = data['email'] || null;

		// Params Validator
		if (!admin_id) {
			return {
				returncode: 400,
				message: 'Admin ID is required.',
				output: []
			};
		}

		// At least one field should be provided for update
		if (!first_name && !last_name && !email) {
			return {
				returncode: 400,
				message: 'At least one field is required for update.',
				output: []
			};
		}

		return await adminsCrud.updateAdminProfile({ 
			admin_id, 
			first_name, 
			last_name, 
			email 
		});
	} catch (error) {
		return {
			returncode: 500,
			message: error.message,
			output: []
		};
	}
}
