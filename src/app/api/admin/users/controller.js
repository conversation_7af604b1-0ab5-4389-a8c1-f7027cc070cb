import adminsCrud from "@/app/lib/crud/Admins";

export async function add_new_admin(data) {
	try {
		const first_name = data['first_name'] || null;
		const last_name = data['last_name'] || null;
		const email = data['email'] || null;
		const password = data['password'] || null;

		// Params Validator
		if (first_name === null || last_name === null || email === null || password === null) {
			return {
				returncode: 400,
				message: 'Missing some params.',
				output: []
			}
		}
		return await adminsCrud.AddNewUser({ first_name, last_name, email, password });

	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function fetch_users() {
	try {
		return await adminsCrud.ReadUsers();
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function update_password(data) {
	try {
		const email = data['email'] || null;
		const password = data['password'] || null;

		// Params Validator
		if (email === null || password === null) {
			return {
				returncode: 400,
				message: 'Missing some params.',
				output: []
			}
		}
		return await adminsCrud.updatePassword({ email, password });

	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function delete_user(id) {
	try {
		if (!id) {
			return { returncode: 400, message: "User ID is required", output: [] };
		}

		return await adminsCrud.deleteUser({ id });
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}
