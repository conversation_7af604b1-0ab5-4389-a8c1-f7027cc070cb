import adminsCrud from "@/app/lib/crud/Admins";

export async function login_admin(data) {
	try {
		const email = data['email'] || null;
		const password = data['password'] || null;

		// Params Validator
		if (email === null || password === null) {
			return {
				returncode: 400,
				message: 'Missing some params.',
				output: []
			}
		}
		return await adminsCrud.Login({ email, password });
	} catch (error) {
		return {
			returncode: 500,
			message: error.message,
			output: []
		};
	}
}
