import productsCrud from "@/app/lib/crud/Products";
import slugify from "slugify";
import { unlink } from "fs/promises";
import path from "path";

export async function add_product(data, tokenData) {
	try {
		const product_name = data['product_name'] || null;
		const description = data['description'] || null;
		const image = data['image'] || null;
		const category_id = data['category_id'] || null;
		const mrp = data['mrp'] || null;
		const selling_price = data['selling_price'] || null;
		const quantity_type = data['quantity_type'] || null;
		const available_quantity = data['available_quantity'] || null;
		const limit = data['limit'] || 10;
		const admin_id = tokenData?.id || null;

		// Params Validation
		if (product_name === null || image === null || category_id === null || mrp === null || quantity_type === null || available_quantity === null || admin_id === null) {
			return { returncode: 400, message: 'Missing required params.', output: [] }
		}

		// Image validation
		if (!image.base64Data || !image.contentType || !image.name) {
			return { returncode: 400, message: 'Missing Image.', output: [] }
		}

		// Category checking
		const exists = await productsCrud.productExists({ product_name });
		if (exists.returncode !== 400) {
			return exists;
		}

		// Actual Working
		const slug = slugify(product_name, { replacement: '-', lower: true, locale: 'vi', trim: true });
		const imageData = await productsCrud.ImageCreation(image, slug);
		return await productsCrud.addProduct({ product_name, description, imageData, category_id, limit, mrp, selling_price, quantity_type, available_quantity, slug, admin_id });

	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function fetch_products() {
	try {
		return await productsCrud.getAllProducts();
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function fetch_products_by_category({ category_id }) {
	try {
		return await productsCrud.getProductsByCategory({ category_id });
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function delete_product(id) {
	try {
		if (!id) {
			return { returncode: 400, message: "Product ID is required", output: [] };
		}

		const exists = await productsCrud.productExistsById({ id });
		if (exists.returncode !== 200 && !exists.output) {
			return { returncode: 400, message: "Product Doesn't Exists", output: [] };
		}

		const product = exists.output;
		try {
			const fileName = product.Images[0].url.split('/').pop();
			const filePath = path.join(process.cwd(), 'public', 'uploads', 'products', product.Slug, fileName);
			await unlink(filePath);
		} catch (error) {
			return { returncode: 500, message: 'Error occured while deleting the image.', output: [] };
		}

		// Delete the category from database
		return await productsCrud.deleteProduct({ id });
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function update_product_info(data, tokenData) {
	try {
		const product_id = data['product_id'] || null;
		const product_name = data['product_name'] || null;
		const description = data['description'] || null;
		const mrp = data['mrp'] || null;
		const selling_price = data['selling_price'] || null;
		const quantity_type = data['quantity_type'] || null;
		const available_quantity = data['available_quantity'] || null;
		const limit = data['limit'] || null;
		const admin_id = tokenData?.id || null;

		// Params Validation
		if (product_name === null || mrp === null || quantity_type === null || available_quantity === null || admin_id === null) {
			return { returncode: 400, message: 'Missing required params.', output: [] }
		}

		// Category checking
		const exists = await productsCrud.productExists({ product_name });
		if (exists.returncode !== 200) {
			return exists;
		}

		// Actual Working
		return await productsCrud.updateInfo({ product_id, product_name, description, mrp, selling_price, quantity_type, available_quantity, limit, admin_id });

	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}
