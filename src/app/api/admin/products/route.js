import { NextResponse } from "next/server";
import { add_product, fetch_products, update_product_info, delete_product, fetch_products_by_category } from "./controller";
import { verifyToken } from "@/app/lib/utils/jwt";

export async function POST(request) {
	try {
		// Get token from cookie
		const token = request.cookies.get('admin_auth_token')?.value;
		if (!token) {
			return NextResponse.json(
				{ returncode: 401, message: "No token provided", output: [] },
				{ status: 401, statusText: "No token provided" }
			);
		}

		// Verify the token
		const userData = verifyToken(token);
		if (!userData) {
			return NextResponse.json(
				{ returncode: 401, message: "Invalid or expired token", output: [] },
				{ status: 401, statusText: "Invalid or expired token" }
			);
		}

		const data = await request.json();
		const result = await add_product(data, userData);
		return NextResponse.json(result, { status: result.returncode });
	} catch (error) {
		return NextResponse.json(
			{ returncode: 500, message: error.message, output: [] },
			{ status: 500 }
		);
	}
}

export async function GET(request) {
	try {
		const { searchParams } = new URL(request?.url);
		const category_id = searchParams?.get('category_id') || null;
		let result;
		if (category_id) {
			result = await fetch_products_by_category({ category_id });
		} else {
			result = await fetch_products();
		}
		return NextResponse.json(result, { status: result.returncode });
	} catch (error) {
		return NextResponse.json(
			{ returncode: 500, message: error.message, output: [] },
			{ status: 500 }
		);
	}
}

export async function PATCH(request) {
	try {
		// Get token from cookie
		const token = request.cookies.get('admin_auth_token')?.value;
		if (!token) {
			return NextResponse.json(
				{ returncode: 401, message: "No token provided", output: [] },
				{ status: 401, statusText: "No token provided" }
			);
		}

		// Verify the token
		const userData = verifyToken(token);
		if (!userData) {
			return NextResponse.json(
				{ returncode: 401, message: "Invalid or expired token", output: [] },
				{ status: 401, statusText: "Invalid or expired token" }
			);
		}

		const data = await request.json();
		const result = await update_product_info(data, userData);
		return NextResponse.json(result, { status: result.returncode });
	} catch (error) {
		return NextResponse.json(
			{ returncode: 500, message: error.message, output: [] },
			{ status: 500 }
		);
	}
}

export async function DELETE(request) {
	try {
		// Get token from cookie
		const token = request.cookies.get('admin_auth_token')?.value;
		if (!token) {
			return NextResponse.json(
				{ returncode: 401, message: "No token provided", output: [] },
				{ status: 401, statusText: "No token provided" }
			);
		}

		// Verify the token
		const userData = verifyToken(token);
		if (!userData) {
			return NextResponse.json(
				{ returncode: 401, message: "Invalid or expired token", output: [] },
				{ status: 401, statusText: "Invalid or expired token" }
			);
		}

		const { searchParams } = new URL(request.url);
		const id = searchParams.get('id');
		const result = await delete_product(id);
		return NextResponse.json(result, { status: result.returncode });
	} catch (error) {
		return NextResponse.json(
			{ returncode: 500, message: error.message, output: [] },
			{ status: 500 }
		);
	}
}
