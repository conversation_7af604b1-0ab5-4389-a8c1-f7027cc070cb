import slidesCrud from "@/app/lib/crud/Slides";
import slugify from "slugify";
import { unlink } from "fs/promises";
import path from "path";

export async function add_slides(data, tokenData) {
	try {
		const slide_name = data['slide_name'] || null;
		const image = data['image'] || null;
		const type = data['type'] || null;
		const admin_id = tokenData?.id || null;

		// Params Validation
		if (slide_name === null || image === null || admin_id === null) {
			return { returncode: 400, message: 'Missing required params.', output: [] }
		}

		// Image validation
		if (!image.base64Data || !image.contentType || !image.name) {
			return { returncode: 400, message: 'Missing Image.', output: [] }
		}

		// checking
		const exists = await slidesCrud.slideExists({ slide_name });
		if (exists.returncode !== 400) {
			return exists;
		}

		// Actual Working
		const slug = slugify(slide_name, { replacement: '-', lower: true, locale: 'vi', trim: true });
		const imageData = await slidesCrud.ImageCreation(image, slug);
		return await slidesCrud.createSlide({ slide_name, imageData, slug, type, admin_id });

	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function fetch_slides() {
	try {
		return await slidesCrud.getAllSlides();
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function delete_slide(id) {
	try {
		if (!id) {
			return { returncode: 400, message: "Slide ID is required", output: [] };
		}

		const exists = await slidesCrud.slideExistsById({ id });
		if (exists.returncode !== 200 && !exists.output) {
			return { returncode: 400, message: "Slide Doesn't Exists", output: [] };
		}

		const slide = exists.output;
		try {
			const fileName = slide.Image.url.split('/').pop();
			const filePath = path.join(process.cwd(), 'public', 'uploads', 'slides', slide.Slug, fileName);
			await unlink(filePath);
		} catch (error) {
			return { returncode: 500, message: 'Error occured while deleting the image.', output: [] };
		}

		// Delete the category from database
		return await slidesCrud.deleteSlide({ id });
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}
