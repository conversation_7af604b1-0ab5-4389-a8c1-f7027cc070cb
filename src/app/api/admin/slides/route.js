import { NextResponse } from "next/server";
import { add_slides, fetch_slides, delete_slide } from "./controller";
import { verifyToken } from "@/app/lib/utils/jwt";

export async function POST(request) {
	try {
		// Get token from cookie
		const token = request.cookies.get('admin_auth_token')?.value;
		if (!token) {
			return NextResponse.json(
				{ returncode: 401, message: "No token provided", output: [] },
				{ status: 401, statusText: "No token provided" }
			);
		}

		// Verify the token
		const userData = verifyToken(token);
		if (!userData) {
			return NextResponse.json(
				{ returncode: 401, message: "Invalid or expired token", output: [] },
				{ status: 401, statusText: "Invalid or expired token" }
			);
		}

		const data = await request.json();
		const result = await add_slides(data, userData);
		return NextResponse.json(result, { status: result.returncode });
	} catch (error) {
		return NextResponse.json(
			{ returncode: 500, message: error.message, output: [] },
			{ status: 500 }
		);
	}
}

export async function GET() {
	try {
		const result = await fetch_slides();
		return NextResponse.json(result, { status: result.returncode });
	} catch (error) {
		return NextResponse.json(
			{ returncode: 500, message: error.message, output: [] },
			{ status: 500 }
		);
	}
}

export async function DELETE(request) {
	try {
		// Get token from cookie
		const token = request.cookies.get('admin_auth_token')?.value;
		if (!token) {
			return NextResponse.json(
				{ returncode: 401, message: "No token provided", output: [] },
				{ status: 401, statusText: "No token provided" }
			);
		}

		// Verify the token
		const userData = verifyToken(token);
		if (!userData) {
			return NextResponse.json(
				{ returncode: 401, message: "Invalid or expired token", output: [] },
				{ status: 401, statusText: "Invalid or expired token" }
			);
		}

		const { searchParams } = new URL(request.url);
		const id = searchParams.get('id');
		const result = await delete_slide(id);
		return NextResponse.json(result, { status: result.returncode });
	} catch (error) {
		return NextResponse.json(
			{ returncode: 500, message: error.message, output: [] },
			{ status: 500 }
		);
	}
}
