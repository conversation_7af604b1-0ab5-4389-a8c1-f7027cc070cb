import productCategoriesCrud from "@/app/lib/crud/ProductCategory";
import slugify from "slugify";
import { unlink } from "fs/promises";
import path from "path";

export async function create_category(data, tokenData) {
	try {
		const category_name = data?.category_name || null;
		const description = data?.description || null;
		const image = data?.image || null;
		const color = data?.color || null;
		const admin_id = tokenData?.id || null;

		// Params Validation
		if (category_name === null || image === null || admin_id === null) {
			return { returncode: 400, message: 'Missing required params.', output: [] }
		}

		// Image validation
		if (!image.base64Data || !image.contentType || !image.name) {
			return { returncode: 400, message: 'Missing Image.', output: [] }
		}

		// Category checking
		const exists = await productCategoriesCrud.categoryExists({ category_name });
		if (exists.returncode !== 400) {
			return exists;
		}

		// Actual Working
		const slug = slugify(category_name, { replacement: '-', lower: true, locale: 'vi', trim: true });
		const imageData = await productCategoriesCrud.ImageCreation(image, slug);
		const result = await productCategoriesCrud.createCategory({ category_name, description, color, imageData, slug });
		return result;

	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function fetch_categories() {
	try {
		return await productCategoriesCrud.getAllCategories();
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function delete_category(id) {
	try {
		if (!id) {
			return { returncode: 400, message: "Category ID is required", output: [] };
		}

		const exists = await productCategoriesCrud.categoryExistsById({ id });
		if (exists.returncode !== 200) {
			return { returncode: 400, message: "Category Doesn't Exists", output: [] };
		}

		const category = exists.output;
		if (category.Image?.url) {
			try {
				const fileName = exists.Image.url.split('/').pop();
				if (fileName) {
					const filePath = path.join(process.cwd(), 'public', 'uploads', 'categories', category.Slug, fileName);
					await unlink(filePath);
				}
			} catch (error) {
				return { returncode: 500, message: error.message, output: [] };
			}
		}

		// Delete the category from database
		return await productCategoriesCrud.deleteByCategory({ id });
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}
