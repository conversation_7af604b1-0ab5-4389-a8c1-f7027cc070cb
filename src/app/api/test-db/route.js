import { NextResponse } from 'next/server';
import dbConnect from '@/app/lib/db/dbConnect';
import mongoose from 'mongoose';

export async function GET() {
	try {
		console.log('🔍 Testing database connection...');
		
		// Test database connection
		await dbConnect();
		
		// Get connection state
		const connectionState = mongoose.connection.readyState;
		const stateMap = {
			0: 'disconnected',
			1: 'connected',
			2: 'connecting',
			3: 'disconnecting'
		};
		
		// Get database info
		const dbInfo = {
			connectionState: stateMap[connectionState] || 'unknown',
			databaseName: mongoose.connection.db?.databaseName || 'unknown',
			host: mongoose.connection.host || 'unknown',
			port: mongoose.connection.port || 'unknown',
			collections: []
		};
		
		// Try to get collections if connected
		if (connectionState === 1) {
			try {
				const collections = await mongoose.connection.db.listCollections().toArray();
				dbInfo.collections = collections.map(col => col.name);
			} catch (collectionError) {
				console.warn('Could not list collections:', collectionError.message);
			}
		}
		
		return NextResponse.json({
			success: true,
			message: 'Database connection test successful',
			data: dbInfo,
			timestamp: new Date().toISOString()
		});
		
	} catch (error) {
		console.error('❌ Database connection test failed:', error);
		
		return NextResponse.json({
			success: false,
			message: 'Database connection test failed',
			error: error.message,
			timestamp: new Date().toISOString()
		}, { status: 500 });
	}
}
