import { register_user } from './controller';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { createToken } from '@/app/lib/utils/jwt';

export async function POST(request) {
	try {
		const data = await request.json();
		const result = await register_user(data);
		if (result.returncode === 200) {
			// Create JWT token
			const userData = result.output;
			const token = createToken({
				id: userData._id,
				email: userData.Email,
				firstName: userData.FirstName,
				lastName: userData.LastName
			});

			// Create response with token in cookie
			const response = NextResponse.json(result);

			// Set httpOnly cookie with token
			response.cookies.set({
				name: 'auth_token',
				value: token,
				httpOnly: true,
				secure: process.env.NODE_ENV === 'production',
				sameSite: 'lax',
				path: '/'
			});

			return response;
		}

		return NextResponse.json(result);
	} catch (error) {
		return NextResponse.json(
			{
				returncode: 500,
				message: error.message,
				output: []
			},
			{ status: 500 }
		);
	}
}
