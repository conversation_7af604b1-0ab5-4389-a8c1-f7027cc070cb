import userAccountsCrud from "@/app/lib/crud/UserAccounts";

export async function register_user(data) {
	try {
		const first_name = data['first_name'] || null;
		const last_name = data['last_name'] || null;
		const email = data['email'] || null;
		const password = data['password'] || null;

		// Params Validator
		if (first_name === null || last_name === null || email === null || password === null) {
			return {
				returncode: 400,
				message: 'Missing some params.',
				output: []
			}
		}
		return await userAccountsCrud.RegisterUser({ first_name, last_name, email, password });
	} catch (error) {
		return {
			returncode: 500,
			message: error.message,
			output: []
		};
	}
}
