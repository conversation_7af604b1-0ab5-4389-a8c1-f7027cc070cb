import userAccountsCrud from "@/app/lib/crud/UserAccounts";

export async function login_user(data) {
	try {
		const email = data['email'] || null;
		const password = data['password'] || null;

		// Params Validator
		if (email === null || password === null) {
			return {
				returncode: 400,
				message: 'Missing some params.',
				output: []
			}
		}
		return await userAccountsCrud.LoginUser({ email, password });
	} catch (error) {
		return {
			returncode: 500,
			message: error.message,
			output: []
		};
	}
}
