import userAccountsCrud from "@/app/lib/crud/UserAccounts";

export async function get_user_profile(user_id) {
	try {
		if (!user_id) {
			return {
				returncode: 400,
				message: 'User ID is required.',
				output: []
			};
		}

		return await userAccountsCrud.getUserProfile({ user_id });
	} catch (error) {
		return {
			returncode: 500,
			message: error.message,
			output: []
		};
	}
}

export async function update_user_profile(user_id, data) {
	try {
		const first_name = data['first_name'] || null;
		const last_name = data['last_name'] || null;
		const email = data['email'] || null;

		// Params Validator
		if (!user_id) {
			return {
				returncode: 400,
				message: 'User ID is required.',
				output: []
			};
		}

		// At least one field should be provided for update
		if (!first_name && !last_name && !email) {
			return {
				returncode: 400,
				message: 'At least one field is required for update.',
				output: []
			};
		}

		return await userAccountsCrud.updateUserProfile({ 
			user_id, 
			first_name, 
			last_name, 
			email 
		});
	} catch (error) {
		return {
			returncode: 500,
			message: error.message,
			output: []
		};
	}
}
