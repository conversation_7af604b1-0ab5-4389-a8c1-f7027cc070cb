import { NextResponse } from "next/server";
import { add_new_order, fetch_orders, fetch_orders_by_userId, delete_order, update_order, fetch_orders_by_id } from "./controller";
import { verifyToken } from "@/app/lib/utils/jwt";

export async function POST(request) {
	try {
		// Get token from cookie
		const token = request.cookies.get('auth_token')?.value;
		if (!token) {
			return NextResponse.json(
				{ returncode: 401, message: "No token provided", output: [] },
				{ status: 401, statusText: "No token provided" }
			);
		}

		// Verify the token
		const userData = verifyToken(token);
		if (!userData) {
			return NextResponse.json(
				{ returncode: 401, message: "Invalid or expired token", output: [] },
				{ status: 401, statusText: "Invalid or expired token" }
			);
		}

		const data = await request.json();
		const result = await add_new_order(data, userData);
		return NextResponse.json(result, { status: result.returncode });
	} catch (error) {
		return NextResponse.json(
			{ returncode: 500, message: error.message, output: [] },
			{ status: 500 }
		);
	}
}

export async function GET(request) {
	try {
		// Get token from cookie
		const token = request.cookies.get('admin_auth_token')?.value;
		const user_token = request.cookies.get('auth_token')?.value;
		if (!token && !user_token) {
			return NextResponse.json(
				{ returncode: 401, message: "No token provided", output: [] },
				{ status: 401, statusText: "No token provided" }
			);
		}

		// Verify the token
		const adminData = verifyToken(token);
		const userData = verifyToken(user_token);
		const user_id = userData.id || null
		if (!adminData && !userData && !user_id) {
			return NextResponse.json(
				{ returncode: 401, message: "Invalid or expired token", output: [] },
				{ status: 401, statusText: "Invalid or expired token" }
			);
		}

		// Filter
		const { searchParams } = new URL(request.url);
		const order_id = searchParams.get('order_id');
		let result;
		if (user_id) {
			if (order_id) {
				result = await fetch_orders_by_id(user_id, order_id);
			} else {
				result = await fetch_orders_by_userId(user_id);
			}
		} else {
			result = await fetch_users();
		}
		return NextResponse.json(result, { status: result.returncode });
	} catch (error) {
		return NextResponse.json(
			{ returncode: 500, message: error.message, output: [] },
			{ status: 500 }
		);
	}
}

export async function PATCH(request) {
	try {
		// Get token from cookie
		const token = request.cookies.get('admin_auth_token')?.value;
		if (!token) {
			return NextResponse.json(
				{ returncode: 401, message: "No token provided", output: [] },
				{ status: 401, statusText: "No token provided" }
			);
		}

		// Verify the token
		const userData = verifyToken(token);
		if (!userData) {
			return NextResponse.json(
				{ returncode: 401, message: "Invalid or expired token", output: [] },
				{ status: 401, statusText: "Invalid or expired token" }
			);
		}

		const data = await request.json();
		const result = await update_order(data);
		return NextResponse.json(result, { status: result.returncode });
	} catch (error) {
		return NextResponse.json(
			{ returncode: 500, message: error.message, output: [] },
			{ status: 500 }
		);
	}
}

// export async function DELETE(request) {
// 	try {
// 		// Get token from cookie
// 		const token = request.cookies.get('admin_auth_token')?.value;
// 		if (!token) {
// 			return NextResponse.json(
// 				{ returncode: 401, message: "No token provided", output: [] },
// 				{ status: 401, statusText: "No token provided" }
// 			);
// 		}
//
// 		// Verify the token
// 		const userData = verifyToken(token);
// 		if (!userData) {
// 			return NextResponse.json(
// 				{ returncode: 401, message: "Invalid or expired token", output: [] },
// 				{ status: 401, statusText: "Invalid or expired token" }
// 			);
// 		}
//
// 		const { searchParams } = new URL(request.url);
// 		const id = searchParams.get('id');
// 		const result = delete_user(id);
// 		return NextResponse.json(result, { status: result.returncode });
// 	} catch (error) {
// 		return NextResponse.json(
// 			{ returncode: 500, message: error.message, output: [] },
// 			{ status: 500 }
// 		);
// 	}
// }
