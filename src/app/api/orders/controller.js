import ordersCrud from "@/app/lib/crud/Orders";

export async function add_new_order(data, userData) {
	try {
		const name = data['name'] || null;
		const contact = data['contact'] || null;
		const apartment = data['apartment'] || null;
		const street_address = data['street_address'] || null;
		const landmark = data['landmark'] || null;
		const city = data['city'] || null;
		const pin_code = data['pin_code'] || null;
		const order_status = data['order_status'] || null;
		const payment_status = data['payment_status'] || null;
		const payment_mode = data['payment_mode'] || null;
		const cart = data['cart'] || null;
		const user_id = userData?.id || null;

		// Params Validator
		if (name === null || contact === null || apartment === null || street_address === null ||
			landmark === null || city === null || pin_code === null || user_id === null || cart === null) {
			return {
				returncode: 400,
				message: 'Missing some params.',
				output: []
			}
		}

		const formattedCart = [];
		await cart.map((cart_item) => {
			const item = {
				ProductId: cart_item.Product.id,
				Quantity: cart_item.Quantity,
				Price: cart_item.Price,
				Savings: (cart_item.Product.Mrp * cart_item.Quantity) - cart_item.Price || 0
			}
			if (!item.Quantity || !item.ProductId || !item.Price) {
				return { returncode: 400, message: 'Missing Cart Params', output: [] };
			}
			formattedCart.push(item);
		});

		return await ordersCrud.createOrder({ name, contact, user_id, apartment, street_address, landmark, city, pin_code, order_status, payment_status, payment_mode, cart: formattedCart });

	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function fetch_orders() {
	try {
		return await ordersCrud.fetchAllOrders();
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function fetch_orders_by_userId(user_id) {
	try {
		return await ordersCrud.fetchOrdersofUser({ user_id });
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function fetch_orders_by_id(user_id, order_id) {
	try {
		return await ordersCrud.fetchOrderInfo({ user_id, order_id });
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}

export async function update_order(data) {
	try {
		const order_id = data.order_id || null;
		const order_status = data.order_status || null;
		const payment_status = data.payment_status || null;
		const payment_mode = data.payment_mode || null;

		// Params Validator
		if (!order_id) {
			return {
				returncode: 400,
				message: 'Missing Order Id.',
				output: []
			};
		}

		let updateResult = null;

		if (order_status !== null) {
			updateResult = await ordersCrud.updateOrderStatus({ order_id, order_status });
		} else if (payment_status !== null) {
			updateResult = await ordersCrud.updatePaymentStatus({ order_id, payment_status });
		} else if (payment_mode !== null) {
			updateResult = await ordersCrud.updatePaymentMode({ order_id, payment_mode });
		} else {
			return {
				returncode: 400,
				message: 'No valid parameters found to update.',
				output: []
			};
		}

		return updateResult;
	} catch (error) {
		return {
			returncode: 500,
			message: error.message,
			output: []
		};
	}
}

export async function delete_order(id) {
	try {
		if (!id) {
			return { returncode: 400, message: "Order ID is required", output: [] };
		}

		return await ordersCrud.deleteOrder({ id });
	} catch (error) {
		return { returncode: 500, message: error.message, output: [] };
	}
}
