"use client";
import Image from 'next/image'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'

// Skeleton component for a single category
const CategorySkeleton = () => (
	<div className="flex flex-col items-center bg-teal-50 gap-2 p-4 rounded-lg animate-pulse">
		<div className="w-[50px] h-[50px] bg-teal-200 rounded-full" />
		<div className="h-4 w-20 bg-teal-200 rounded" />
	</div>
);

function CategoryList({ selected = "" }) {
	const [categories, setCategories] = useState([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		fetchCategories();
	}, []);

	const fetchCategories = async () => {
		try {
			setLoading(true);
			const response = await fetch('/api/admin/product_categories');
			const data = await response.json();
			if (response.ok) {
				setCategories(data.output);
			}
		} catch (error) {
			console.error('Error fetching categories:', error);
		} finally {
			setLoading(false);
		}
	};

	// Generate array of skeleton items
	const skeletonArray = Array(7).fill(null);

	return (
		<div className='mt-5'>
			<div>
				<div className='text-teal-600 font-bold text-2xl'>
					Shop by Category
				</div>
			</div>

			<div className='grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-5 mt-2'>
				{loading ? (
					// Show skeleton UI while loading
					skeletonArray.map((_, index) => (
						<CategorySkeleton key={`skeleton-${index}`} />
					))
				) : (
					// Show actual categories when data is loaded
					categories.map((category, index) => (
						<Link
							href={`/category/${category.Slug}`}
							key={index}
							className={`flex flex-col items-center bg-teal-100 gap-2 p-4 rounded-lg group cursor-pointer hover:bg-teal-600 
              ${selected === category.Name && "bg-teal-600 text-white"}
              `}
						>
							<Image
								src={category.Icon[0].url}
								alt={category.Name}
								width={50}
								height={50}
								className='group-hover:scale-125 transition-all ease-in-out duration-200'
							/>
							<h2
								className={`
                  ${selected === category.Name ? "text-white" : "text-teal-800"}
                  text-sm md:text-base group-hover:text-white
                `}
							>
								{category.Name}
							</h2>
						</Link>
					))
				)}
			</div>
		</div>
	)
}

export default CategoryList
