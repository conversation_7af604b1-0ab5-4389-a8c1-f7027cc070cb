'use client';

import { But<PERSON> } from '@/components/ui/button'
import { TrashIcon } from 'lucide-react'
import Image from 'next/image'
import { useRouter } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/Auth';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { handleQuantityDecrement, handleQuantityIncrement, RemoveItemFromCart, viewCart } from '../globals/Cart';
import { useToast } from '@/hooks/use-toast';
import { ToastAction } from "@/components/ui/toast";

function CartList({ setUpdateCart, updateCart }) {
	const { user } = useAuth();
	const { toast } = useToast();
	const router = useRouter();
	const [subTotal, setSubTotal] = useState(0);
	const [Quantity, setQuantity] = useState(0);
	const [Cart, setCart] = useState([]);

	useEffect(() => {
		fetchCart();
	}, []);

	const fetchCart = async () => {
		try {
			const cart = await viewCart();
			let total_price = 0;
			cart.data.map((cart_item) => {
				total_price += cart_item.Price;
			});
			setCart(cart.data);
			setSubTotal(total_price.toFixed(2));
			setQuantity(Cart.length);
		} catch (error) {
			console.log(error.message);
		}
	}

	// Quantity handlers
	const handleDecrement = async (product_id) => {
		try {
			const cart = await handleQuantityDecrement({ product_id });
			if (cart.success) {
				toast({
					title: 'Removed from Cart',
				});
			}
			else {
				toast({
					title: 'Error Occured',
					description: 'Error decrementing stock quantity.',
					action: <ToastAction> Try Again </ToastAction>
				});
			}
		} catch (error) {
			toast({
				title: 'Error Occured',
				description: 'Error decrementing stock quantity.',
				action: <ToastAction> Try Again </ToastAction>
			});
		} finally {
			await fetchCart();
			setUpdateCart(!updateCart);
		}
	};

	// Delete a product from cart
	const removeItemFromCart = async ({ id }) => {
		try {
			const cart = await RemoveItemFromCart(id);
			if (cart.success) {
				toast({
					title: 'Item Removed'
				})
			} else {
				toast({
					title: 'Error Occured',
					description: 'Some Error occured while removing the item.',
					action: <ToastAction
						altText="Try Again"
						onClick={() => removeItemFromCart({ id })}
					>
						Try Again
					</ToastAction>
				})
			}
		} catch (error) {
			console.log('Error fetching cart:', error);
		} finally {
			fetchCart();
			setUpdateCart(true);
		}
	}


	const handleIncrement = async (product_id) => {
		try {
			const cart = await handleQuantityIncrement({ product_id });
			if (cart.success) {
				toast({
					title: 'Added to cart',
				});
			}
			else {
				toast({
					title: 'Error Occured',
					description: 'Error decrementing stock quantity.',
					action: <ToastAction> Try Again </ToastAction>
				});
			}
		} catch (error) {
			toast({
				title: 'Error Occured',
				description: 'Error decrementing stock quantity.',
				action: <ToastAction> Try Again </ToastAction>
			});
		} finally {
			fetchCart();
			setUpdateCart(!updateCart);
		}
	};

	return (
		<section className='text-gray-700'>
			{/* For Larger Screens */}
			<div className='hidden'>
				{Cart.map((cart_item, index) => (
					<div key={index} className='flex justify-between items-center p-4 border-b mb-5'>
						<Image
							src={cart_item.Product?.Images?.url || '/placeholder.png'}
							alt={cart_item.Product.Name}
							width={90}
							height={90}
							className='rounded-lg'
						/>
						<div className='w-[75%]'>
							<h1 className='font-semibold'>
								{cart_item.Product.Name}
							</h1>
							<h2> Quantity: {cart_item.Quantity} </h2>
							<h3> Rs. {cart_item.Price} </h3>
						</div>
						<div className="p-2 border flex gap-10 items-center px-5">
							<button
								disabled={cart_item.Quantity === 1}
								onClick={handleDecrement}
								className="disabled:opacity-50"
								aria-label="Decrease quantity"
							>
								-
							</button>
							<span>{cart_item.Quantity}</span>
							<button
								onClick={handleIncrement}
								aria-label="Increase quantity"
							>
								+
							</button>
						</div>

						<TrashIcon
							onClick={() => { removeItemFromCart({ id: cart_item.Product.id }); }}
							className='p-1 transition-colors duration-300 hover:bg-primary hover:text-white cursor-pointer rounded-lg w-[30px] h-[30px]' />
					</div>


				))}
			</div>
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead className='w-[100px]'>Image</TableHead>
						<TableHead>Products ({Quantity > 0 ? `${Quantity} items` : `${Quantity} item`})</TableHead>
						<TableHead>Price</TableHead>
						<TableHead className='w-[100px]'>Quantity</TableHead>
						<TableHead>Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{Cart.map((cart_item, index) => (
						<TableRow key={index}>
							<TableCell>
								<Image
									src={cart_item.Product?.Images?.url || '/placeholder.png'}
									alt={cart_item.Product.Name}
									width={1000}
									height={1000}
									className='rounded-lg'
								/>
							</TableCell>

							<TableCell>
								<h1 className='font-semibold flex items-center gap-2'>
									<span className='py-1 text-lg'>
										{cart_item.Product.Name}
									</span>
									<div className=''>
										<p className='text-xs text-center text-primary border border-primary rounded-full py-1 px-2'>
											{cart_item?.Product?.Category || "Category"}
										</p>
									</div>
								</h1>
								<h2> {cart_item?.Product?.Description || "Description"} </h2>
							</TableCell>

							<TableCell>
								<h1 className='font-semibold'>
									Rs. {cart_item.Price.toFixed(2)}
								</h1>
							</TableCell>

							<TableCell>
								<div className="p-2 border flex gap-10 items-center">
									<button
										disabled={cart_item.Quantity === 1}
										onClick={(e) => {
											e.preventDefault();
											handleDecrement(cart_item.Product.id);
										}}
										className="disabled:opacity-50 text-lg  hover:bg-primary hover:text-white py-1 px-2 rounded-lg"
										aria-label="Decrease quantity"
									>
										-
									</button>
									<span className='text-base'>{cart_item.Quantity}</span>
									<button
										onClick={(e) => {
											e.preventDefault();
											handleIncrement(cart_item.Product.id);
										}}
										aria-label="Increase quantity"
										className="text-lg hover:bg-primary hover:text-white py-1 px-2 rounded-lg"
									>
										+
									</button>
								</div>
							</TableCell>

							<TableCell>
								<TrashIcon
									onClick={() => { removeItemFromCart({ id: cart_item.Product.id }); }}
									className='p-1 text-red-500 bg-red-100 cursor-pointer rounded-lg w-[30px] h-[30px]' />
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
		</section>
	)
}

export default CartList
