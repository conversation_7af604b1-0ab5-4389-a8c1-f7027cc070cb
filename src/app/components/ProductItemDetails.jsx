"use client";

import { But<PERSON> } from '@/components/ui/button';
import { ShoppingBasket } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '../contexts/Auth';
import { addToCart } from '../globals/Cart';
import { useContext, useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { ToastAction } from "@/components/ui/toast";
import { CartContext } from '../contexts/Cart';

function ProductItemDetails({ product, setOpen }) {
	const { toast } = useToast();
	const { user } = useAuth();
	const { setUpdateCart } = useContext(CartContext);
	const router = useRouter();
	const initialPrice = product?.SellingPrice ?? product?.MarketRatePrice;
	const [isAddingToCart, setIsAddingToCart] = useState(false);
	const [ProductTotalPrice, setProductTotalPrice] = useState(initialPrice);
	const [Quantity, setQuantity] = useState(1);


	if (!user) {
		router.push('/auth');
		return null; // Return early if no user
	}

	// Quantity handlers
	const handleDecrement = () => {
		setQuantity(prev => {
			const newQuantity = prev - 1;
			setProductTotalPrice(newQuantity * initialPrice);
			return newQuantity;
		});
	};

	const handleIncrement = () => {
		setQuantity(prev => {
			const newQuantity = prev + 1;
			setProductTotalPrice(newQuantity * initialPrice);
			return newQuantity;
		});
	};

	const handleAddToCart = async () => {
		if (isAddingToCart) return;
		setIsAddingToCart(true);
		console.log(product);
		try {
			const result = await addToCart({
				data: {
					Quantity,
					Price: ProductTotalPrice,
					Product: {
						id: product._id,
						Images: product.Images[0],
						Name: product.Name,
						Description: product.Description,
						Category: product.Categories.Name,
						Mrp: product.MarketRatePrice,
						SellingPrice: product.SellingPrice,
					},
				}
			});

			if (result.success) {
				toast({
					title: 'Added Successfully!!',
				});
				// router.refresh();
			} else {
				toast({
					title: 'Something went wrong!',
					action: <ToastAction altText="Try Again">Try Again</ToastAction>,
				});
			}
			if (setOpen) {
				setOpen(false);
			}
		} catch (error) {
			toast({
				title: 'Failed to add to cart!',
				action: <ToastAction altText="Try Again">Try Again</ToastAction>,
			});
		} finally {
			setIsAddingToCart(false);
			setUpdateCart(true);
		}
	};

	if (!product) {
		return (
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-7 bg-white text-black">
				{/* Image skeleton */}
				<Skeleton className="h-[200px] w-[180px] md:h-[320px] md:w-[300px] rounded-lg" />

				<div className="flex flex-col gap-3">
					{/* Title skeleton */}
					<Skeleton className="h-8 w-3/4" />

					{/* Description skeleton */}
					<Skeleton className="h-4 w-full" />
					<Skeleton className="h-4 w-5/6" />

					{/* Category skeleton */}
					<div className="flex gap-3 flex-wrap py-2">
						<Skeleton className="h-6 w-24 rounded-2xl" />
					</div>

					{/* Price skeleton */}
					<div className="flex gap-3">
						<Skeleton className="h-10 w-32" />
					</div>

					{/* Quantity type skeleton */}
					<Skeleton className="h-4 w-32" />

					{/* Per price skeleton */}
					<div className="flex items-center gap-3">
						<Skeleton className="h-4 w-24" />
						<Skeleton className="h-4 w-24" />
					</div>

					{/* Quantity controls and button skeleton */}
					<div className="flex flex-col items-baseline gap-3">
						<Skeleton className="h-10 w-32" />
						<Skeleton className="h-10 w-40" />
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-7 bg-white text-black">
			<Image
				src={product.Images?.[0]?.url ?? '/placeholder.png'}
				alt={product.Name ?? 'Product'}
				width={300}
				height={300}
				className="p-5 h-[200px] w-[180px] md:h-[320px] md:w-[300px] object-contain rounded-lg border"
				priority
			/>

			<div className="flex flex-col gap-3">
				<h1 className="text-2xl font-semibold">{product.Name}</h1>
				<p className="text-sm text-gray-500">{product.Description}</p>

				<div className="flex gap-3 flex-wrap py-2">
					{product?.Categories?.Slug && (
						<Link
							href={`/category/${product.Categories.Slug}`}
							className="text-teal-800 border border-teal-800 px-3 py-1 rounded-2xl text-xs"
						>
							{product.Categories.Name ?? 'Loading...'}
						</Link>
					)}
				</div>

				<div className="flex gap-3">
					<h2 className="text-3xl font-semibold">
						Rs. {ProductTotalPrice.toFixed(2)}
					</h2>
				</div>

				<h2 className="text-sm">Quantity ({product.QuantityType})</h2>

				<div className="flex items-center gap-3 text-sm text-teal-800">
					<p>Per Price:-</p>
					{product?.SellingPrice && <p>Rs. {product.SellingPrice}</p>}
					<p className={product?.SellingPrice ? "line-through text-teal-700 opacity-50" : ""}>
						Rs. {product.MarketRatePrice}
					</p>
				</div>

				<div className="flex flex-col items-baseline gap-3">
					<div className="p-2 border flex gap-10 items-center px-5">
						<button
							disabled={Quantity === 1}
							onClick={handleDecrement}
							className="disabled:opacity-50"
							aria-label="Decrease quantity"
						>
							-
						</button>
						<span>{Quantity}</span>
						<button
							onClick={handleIncrement}
							aria-label="Increase quantity"
						>
							+
						</button>
					</div>

					<Button
						className="flex gap-3"
						onClick={handleAddToCart}
						disabled={isAddingToCart}
					>
						<ShoppingBasket className="w-6 h-6" />
						{isAddingToCart ? 'Adding...' : 'Add To Cart'}
					</Button>
				</div>
			</div>
		</div>
	);
}

export default ProductItemDetails;
