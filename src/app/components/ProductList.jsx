'use client';

import React, { useState, useEffect, useContext } from 'react'
import { ProductItem, ProductItemSkeleton } from './ProductItem';
import { viewCart } from '../globals/Cart';
import { CartContext } from '../contexts/Cart';

function ProductList() {
	const { updateCart, setUpdateCart } = useContext(CartContext);
	const [Products, setProducts] = useState([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		fetchProducts();
	}, [updateCart]);

	const fetchProducts = async () => {
		try {
			const cart = await viewCart();
			const response = await fetch('/api/admin/products');
			const data = await response.json();
			if (response.ok && data?.output?.length > 0) {
				const updatedProductData = data.output.map(product => ({
					...product,
					inCart: cart?.data?.some((cartItem) => {
						if (cartItem.Product.id === product._id) {
							return true
						}
					})
				}));
				setProducts(updatedProductData);
			}
		} catch (error) {
			console.log('Error fetching Products:', error);
		} finally {
			setLoading(false);
			setUpdateCart(false);
		}
	};

	// Generate array of skeleton items
	const skeletonArray = Array(7).fill(null);

	return (
		<div className='mt-10'>
			<h1 className='text-teal-600 font-bold text-2xl pb-6'>Our Popular Products</h1>
			<div className='grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5'>
				{
					loading ? (
						skeletonArray.map((_, index) => (
							<ProductItemSkeleton key={`skeleton-${index}`} />
						))
					) : (
						Products.map((product, index) => (
							<ProductItem product_data={product} key={index} />
						))
					)
				}
			</div>
		</div>
	)
}

export default ProductList
