"use client";
import { useEffect, useState } from "react";
import { animated_logo } from "./Logo";
import Header from "./Header";
import Footer from "./Footer";
import { Toaster } from "@/components/ui/toaster";
import { AuthContextProvider } from "../contexts/Auth";
import { usePathname } from "next/navigation";
import { CartContext } from "../contexts/Cart";

export default function RootSkeleton({ children }) {
	const pathname = usePathname();
	let auth_path; let admin_path;
	const [show, setshow] = useState(false); // Loading
	const [Show, setShow] = useState(false); // Auth
	const [updateCart, setUpdateCart] = useState(false); // Cart

	useEffect(() => {
		auth_path = pathname.startsWith("/auth")
		admin_path = pathname.startsWith("/admin")
		// Show main layout if NOT in auth or admin paths
		setShow(!auth_path && !admin_path);

		if (!admin_path) {
			const timer = setTimeout(() => {
				setshow(true);
			}, 3000);

			return () => clearTimeout(timer);
		}
	}, [pathname]);

	return (
		<>
			{!show && admin_path ? (
				<div className="flex items-center justify-center h-screen">
					{animated_logo}
				</div>
			) : (
				<>
					<Toaster />
					<CartContext.Provider value={{ updateCart, setUpdateCart }}>
						<AuthContextProvider>
							{Show && (
								<Header />
							)}
							{children}
							{Show && (
								<Footer />
							)}
						</AuthContextProvider>
					</CartContext.Provider>
				</>
			)}
		</>
	);
}
