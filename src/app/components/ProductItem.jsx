'use client';

import { But<PERSON> } from '@/components/ui/button'
import Image from 'next/image'
import React, { useState } from 'react'
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog"
import ProductItemDetails from './ProductItemDetails'
import Link from 'next/link';

// Skeleton component
export const ProductItemSkeleton = () => (
	<div className='p-2 md:p-6 flex flex-col items-center justify-between gap-3 border rounded-lg'>
		{/* Image skeleton */}
		<div className='animate-pulse bg-gray-200 lg:h-[200px] lg:w-[300px] md:h-[200px] md:w-[300px] rounded-lg' />

		{/* Content skeleton */}
		<div className='flex flex-col items-center justify-between gap-1 w-full'>
			{/* Title skeleton */}
			<div className='animate-pulse bg-gray-200 h-6 w-3/4 rounded-md' />

			{/* Price skeleton */}
			<div className='flex gap-3 items-center'>
				<div className='animate-pulse bg-gray-200 h-5 w-20 rounded-md' />
				<div className='animate-pulse bg-gray-200 h-5 w-20 rounded-md' />
			</div>

			{/* Button skeleton */}
			<div className='animate-pulse bg-gray-200 h-9 w-24 rounded-lg mt-1' />
		</div>
	</div>
);

export function ProductItem({ product_data, loading }) {
	const [open, setOpen] = useState(false);

	if (loading) {
		return <ProductItemSkeleton />;
	}

	return (
		<div className='p-2 md:p-6 flex flex-col items-center justify-between gap-3 border rounded-lg transition-all ease-in-out hover:scale-105 hover:shadow-lg cursor-pointer'>
			<div className='relative w-full'>
				{
					product_data?.inCart && (
						<h1 className='text-red-500 border border-red-500 px-4 py-2 absolute left-0 text-xs  rounded-full'>
							Added to Cart
						</h1>
					)
				}
			</div>
			<div>
				<Image
					src={product_data.Images[0].url}
					alt={product_data.Name}
					width={1500}
					height={1500}
					className='object-contain lg:h-[200px] lg:w-[300px] md:h-[200px] md:w-[300px]'
				/>
			</div>
			<div className='flex flex-col items-center justify-between gap-1'>
				<h1 className='font-semibold'>{product_data.Name}</h1>
				<div className='flex gap-3 items-center'>
					{product_data?.SellingPrice && (
						<p>Rs. {product_data?.SellingPrice}</p>
					)}
					<p className={`${product_data?.SellingPrice && "line-through text-gray-700 opacity-50"}`}>
						Rs. {product_data.MarketRatePrice}
					</p>
				</div>
				{
					product_data.inCart ? (
						<Link href={'/cart'} className='bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/80'>
							View Cart
						</Link>
					) : (
						<Dialog open={open} onOpenChange={setOpen}>
							<DialogTrigger asChild>
								<Button variant='outline' className="text-primary border-teal-500 hover:bg-teal-500 hover:text-white rounded-lg">
									View
								</Button>
							</DialogTrigger>
							<DialogContent>
								<DialogHeader>
									<DialogTitle></DialogTitle>
								</DialogHeader>
								<div>
									<ProductItemDetails product={product_data} setOpen={setOpen} />
								</div>
							</DialogContent>
						</Dialog >
					)
				}
			</div >
		</div >
	)
}
