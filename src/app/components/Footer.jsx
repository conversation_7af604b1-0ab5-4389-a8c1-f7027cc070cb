import React, { useEffect, useState } from 'react'
import { logo } from './Logo'
import { CompanyName } from './Constants';

function Footer() {
	const currentYear = new Date().getFullYear();

	return (
		<footer className="mt-7  bg-teal-100">
			<div className="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8">
				<div className="lg:grid lg:grid-cols-2">
					<div
						className="border-b border-gray-100 py-8 lg:order-last lg:border-s lg:border-b-0 lg:py-16 lg:ps-16"
					>
						<div className="block text-teal-600 lg:hidden">
							{logo}
						</div>

						<div className="mt-8 space-y-4 lg:mt-0">
							<span className="hidden h-1 w-10 rounded-sm bg-teal-500 lg:block"></span>

							<div>
								<h2 className="text-2xl font-medium text-gray-900">Any queries for us</h2>

								<p className="mt-4 max-w-lg text-gray-500">
									Drop your email if you have any dissatisfaction or queries, our team will contact you as soon as possible.
								</p>
							</div>

							<form className="mt-6 w-full">
								<label htmlFor="UserEmail" className="sr-only"> Email </label>

								<div
									className="rounded-md sm:flex sm:items-center sm:gap-4"
								>
									<div className='sm:pb-0 lg:pb-0 md:pb-0 pb-4'>
										<input
											type="email"
											id="UserEmail"
											placeholder="<EMAIL>"
											className="w-full border-none bg-white px-4 py-2"
										/>
									</div>

									<button
										className="mt-1 w-full rounded-sm bg-teal-500 px-6 py-3 text-sm font-bold tracking-wide text-white uppercase transition-none hover:bg-teal-600 sm:mt-0 sm:w-auto sm:shrink-0"
									>
										Contact Me
									</button>
								</div>
							</form>
						</div>
					</div>

					<div className="py-8 lg:py-16 lg:pe-16">
						<div className="hidden text-teal-600 lg:block">
							{logo}
						</div>

						<div className="mt-8 grid grid-cols-1 gap-8 sm:grid-cols-3">
							<div>
								<p className="font-medium text-gray-900">Company</p>

								<ul className="mt-6 space-y-4 text-sm">
									<li>
										<a href="#" className="text-gray-700 transition hover:opacity-75"> About </a>
									</li>

									<li>
										<a href="#" className="text-gray-700 transition hover:opacity-75"> Meet the Team </a>
									</li>

									<li>
										<a href="#" className="text-gray-700 transition hover:opacity-75"> Accounts Review </a>
									</li>
								</ul>
							</div>

							<div>
								<p className="font-medium text-gray-900">Helpful Links</p>

								<ul className="mt-6 space-y-4 text-sm">
									<li>
										<a href="/" className="text-gray-700 transition hover:opacity-75"> Terms & Conditions </a>
									</li>

									<li>
										<a href="/" className="text-gray-700 transition hover:opacity-75"> Privacy Policy </a>
									</li>

									<li>
										<a href="/" className="text-gray-700 transition hover:opacity-75"> Cookies </a>
									</li>
								</ul>
							</div>
						</div>

						<div className="mt-8 border-t border-gray-100 pt-8">
							<p className="mt-8 text-xs text-gray-500">&copy; {currentYear}. {CompanyName}. All rights reserved.</p>
						</div>
					</div>
				</div>
			</div>
		</footer>
	)
}

export default Footer
