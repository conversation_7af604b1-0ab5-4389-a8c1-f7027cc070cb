'use client';

import React, { useState, useEffect } from 'react'
import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious,
} from "@/components/ui/carousel"
import Image from 'next/image'

const Skeleton = () => (
	<CarouselItem className="animate-pulse">
		<div className="w-full h-[200px] md:h-[500px] bg-gray-400 rounded-2xl" />
	</CarouselItem >
);

function Slider() {

	const [Slides, setSlides] = useState([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		fetchSlides();
	}, []);

	const fetchSlides = async () => {
		try {
			const response = await fetch('/api/admin/slides');
			const data = await response.json();
			if (response.ok && data.output.length > 0) {
				setSlides(data.output);
			}
		} catch (error) {
			console.log('Error fetching Slides:', error);
		} finally {
			setLoading(false);
		}
	};

	// Generate array of skeleton items
	const skeletonArray = Array(4).fill(null);

	return (
		<>
			{/* Slider */}
			<Carousel>
				<CarouselContent>
					{
						loading ? (
							skeletonArray.map((_, index) => (
								<Skeleton key={`skeleton-${index}`} />
							))
						) : (
							Slides.map((slide, index) => (
								<CarouselItem key={index}>
									<Image
										src={slide.Image.url}
										alt={slide.Name}
										width={2500}
										height={2500}
										className='w-full h-[200px] md:h-[500px] object-cover rounded-2xl'
									/>
								</CarouselItem>
							))
						)
					}
				</CarouselContent>
				<CarouselPrevious />
				<CarouselNext />
			</Carousel>
		</>
	)
}

export default Slider
