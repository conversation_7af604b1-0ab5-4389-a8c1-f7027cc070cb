import React from 'react'
import { ProductItem, ProductItemSkeleton } from './ProductItem'

function ProductListFilter({ Products, display_skeleton = false }) {
	// Generate array of skeleton items
	const skeletonArray = Array(4).fill(null);

	return (
		<div className='mt-10'>
			<h1 className='text-teal-600 font-bold text-2xl pb-6'>Our Popular Products</h1>
			<div className='grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5'>
				{
					display_skeleton ? (
						skeletonArray.map((_, index) => (
							<ProductItemSkeleton key={`skeleton-${index}`} />
						))
					) : (
						Products.map((product, index) => (
							<ProductItem product_data={product} key={index} />
						))
					)
				}
			</div>
		</div>
	)
}

export default ProductListFilter
