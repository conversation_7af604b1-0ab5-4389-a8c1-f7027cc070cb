"use client";
import { But<PERSON> } from '@/components/ui/button';
import { LayoutGrid, LogOutIcon, Search, ShoppingBag, UserCircleIcon } from 'lucide-react';
import Image from 'next/image';
import React, { useContext, useEffect, useState } from 'react'
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { logo } from './Logo';
import Link from 'next/link';
import { useAuth } from '../contexts/Auth';
import { RemoveItemFromCart, viewCart } from '../globals/Cart';
import { CartContext } from '../contexts/Cart';
import { toast } from '@/hooks/use-toast';
import { ToastAction } from '@/components/ui/toast';

function Header() {
	const { user, logout } = useAuth();
	const { updateCart, setUpdateCart } = useContext(CartContext);
	const [Category, setCategory] = useState([]);
	const [Cart, setCart] = useState([]);


	useEffect(() => {
		fetchCategories();
	}, [user]);

	useEffect(() => {
		fetchCart();
	}, [updateCart])


	const fetchCategories = async () => {
		try {
			const response = await fetch('/api/admin/product_categories');
			const data = await response.json();
			if (response.ok) {
				setCategory(data.output);
			}
		} catch (error) {
			console.log('Error fetching categories:', error);
		}
	};

	const fetchCart = async () => {
		try {
			const cart = await viewCart();
			setCart(cart.data);
		} catch (error) {
			console.log('Error fetching cart:', error);
		} finally {
			setUpdateCart(false);
		}
	}

	const removeItemFromCart = async ({ id }) => {
		try {
			const cart = await RemoveItemFromCart(id);
			if (cart.success) {
				toast({
					title: 'Item Removed'
				})
			} else {
				toast({
					title: 'Error Occured',
					description: 'Some Error occured while removing the item.',
					action: <ToastAction
						altText="Try Again"
						onClick={() => removeItemFromCart({ id })}
					>
						Try Again
					</ToastAction>
				})
			}
		} catch (error) {
			console.log('Error fetching cart:', error);
		} finally {
			fetchCart();
		}
	}

	return (
		<nav className='p-5 shadow-sm flex items-center justify-between'>
			<div className='flex items-center gap-8 '>

				<Link href='/' className="block text-teal-600">
					{logo}
				</Link>
				<div className='md:flex hidden'>
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<h2 className='flex gap-2 items-center p-2 px-10 bg-slate-200 border rounded-full cursor-pointer'>
								<LayoutGrid className='w-5 h-5' /> Category
							</h2>
						</DropdownMenuTrigger>
						<DropdownMenuContent>
							<DropdownMenuLabel>Browse Category</DropdownMenuLabel>
							<DropdownMenuSeparator />
							{
								Category.map((category, index) => (
									<Link href={`/category/${category.Slug}`} key={index}>
										<DropdownMenuItem className="flex gap-4 items-center cursor-pointer">
											<Image
												src={category.Icon[0].url}
												width={40}
												height={40}
												alt={category.Name}
											/>
											<h2 className='text-lg'> {category.Name} </h2>
										</DropdownMenuItem>
									</Link>
								))
							}
						</DropdownMenuContent>
					</DropdownMenu>

				</div>
				<div className='md:flex hidden'>
					<div className='flex gap-3 items-center border rounded-full p-2 px-5 '>
						<Search />
						<input type="text" placeholder='Search' className='outline-none' />
					</div>
				</div>
			</div>
			<div className='flex gap-5 items-center'>
				<Link href={'/cart'} className='flex items-center gap-2 text-lg  rounded-xl' aria-label='Cart Page'>
					<ShoppingBag />
					<p className={`${Cart.length > 0 ? 'absolute top-6 right-[70px] bg-primary p-1.5 rounded-full' : 'hidden'}`}></p>
				</Link>
				{
					user ? (
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<UserCircleIcon className='h-10 w-10 text-primary bg-teal-100 p-1 rounded-full cursor-pointer' />
							</DropdownMenuTrigger>
							<DropdownMenuContent className='px-4'>
								<DropdownMenuLabel>{user?.firstName + " " + user?.lastName}</DropdownMenuLabel>
								<DropdownMenuSeparator />
								<DropdownMenuItem className='cursor-pointer'>
									<Link href='/profile' className='w-full'>
										Profile
									</Link>
								</DropdownMenuItem>
								<DropdownMenuItem className='cursor-pointer'>
									<Link href='/orders' className='w-full'>
										My Orders
									</Link>
								</DropdownMenuItem>
								<DropdownMenuItem onClick={logout} className='cursor-pointer flex justify-between'>
									Logout <LogOutIcon className='text-gray-400' />
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					) : (
						<Link href='/auth'>
							<Button> Sign Up </Button>
						</Link>
					)
				}
			</div>
		</nav>
	)
}

export default Header;
