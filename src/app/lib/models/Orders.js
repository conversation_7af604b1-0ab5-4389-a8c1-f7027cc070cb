import mongoose from "mongoose";

const CartSchema = new mongoose.Schema(
	{
		ProductId: { type: mongoose.Schema.Types.ObjectId, ref: 'Products' },
		Quantity: { type: Number, required: true },
		Price: { type: Number, required: true },
		Savings: { type: Number },
	}
)

export const OrdersSchema = new mongoose.Schema(
	{
		// Personal Info
		Name: { type: String, required: true },
		Contact: { type: String, required: true, },
		User: { type: mongoose.Schema.Types.ObjectId, ref: 'UserAccounts', required: true },

		// Address
		Apartment: { type: String, required: true },
		StreetAddress: { type: String, required: true },
		Landmark: { type: String, required: true },
		Pincode: { type: String, required: true },
		City: { type: String, required: true },

		// Status 
		OrderStatus: { type: String, required: true, default: 'Order Placed' },
		PaymentStatus: { type: String, required: true, default: 'Paid' },
		PaymentMode: { type: String, required: true, default: 'Cash on Delivery' },

		// Cart
		Cart: { type: [CartSchema], required: true }

	}, { timestamps: true }
);

// Pre-save middleware to update ordersCount in Products collection
OrdersSchema.pre("save", async function(next) {
	try {
		const Products = mongoose.model("Products");

		await Promise.all(
			this.Cart.map(async (item) => {
				await Products.findByIdAndUpdate(item.ProductId, {
					$inc: { OrdersCount: 1 },
				});
			})
		);
		next();
	} catch (error) {
		next(error);
	}
});

export default mongoose.models.Orders || mongoose.model('Orders', OrdersSchema);
