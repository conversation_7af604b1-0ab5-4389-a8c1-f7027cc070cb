import mongoose from "mongoose";

const ImageSchema = new mongoose.Schema({
	name: {
		type: String,
		required: true
	},
	contentType: {
		type: String,
		required: true
	},
	description: {
		type: String,
		required: false
	},
	url: {
		type: String,
		required: true
	}
});

export const ProductCategoriesSchema = new mongoose.Schema(
	{
		Name: { type: String, required: true, unique: true },
		Description: { type: String },
		Icon: { type: [ImageSchema], required: true },
		Color: { type: String },
		Slug: { type: String, required: true, unique: true },
		// Child Relationship
		createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Admins' },
		Products: [
			{ type: mongoose.Schema.Types.ObjectId, ref: 'Products' }
		]
	}, { timestamps: true }
);

// Add pre-remove middleware
ProductCategoriesSchema.pre('remove', async function(next) {
	try {
		// Delete all products in this category
		const Products = mongoose.model('Products');
		await Products.deleteMany({ CategoryId: this._id });
		next();
	} catch (error) {
		next(error);
	}
});

// Add pre-deleteMany middleware
ProductCategoriesSchema.pre('deleteMany', async function(next) {
	try {
		// Get categories to be deleted
		const ProductCategories = mongoose.model('ProductCategories');
		const categories = await ProductCategories.find(this.getFilter());
		const categoryIds = categories.map(category => category._id);

		if (categoryIds.length > 0) {
			// Run cleanup operations in parallel
			await Promise.all([
				// Delete all items in these categories
				mongoose.model('Products').deleteMany({ CategoryId: { $in: categoryIds } }),
			]);
		}
		next();
	} catch (error) {
		next(error);
	}
});

export default mongoose.models.ProductCategories || mongoose.model("ProductCategories", ProductCategoriesSchema);
