import mongoose from "mongoose";

const ImageSchema = new mongoose.Schema({
	name: {
		type: String,
		required: true
	},
	contentType: {
		type: String,
		required: true
	},
	description: {
		type: String,
		required: false
	},
	url: {
		type: String,
		required: true
	}
});

export const SlidesSchema = new mongoose.Schema(
	{
		Name: { type: String, required: true, unique: true },
		Image: { type: ImageSchema, required: true },
		Type: { type: String },
		Slug: { type: String, required: true, unique: true },
		createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Admins' },
		updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Admins' },
	}, { timestamps: true }
);

export default mongoose.models.Slides || mongoose.model("Slides", SlidesSchema);
