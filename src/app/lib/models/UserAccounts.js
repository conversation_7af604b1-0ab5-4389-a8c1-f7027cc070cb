import mongoose from "mongoose";
import bcrypt from "bcryptjs";

export const UserAccountsSchema = new mongoose.Schema(
	{
		FirstName: { type: String, required: true },
		LastName: { type: String, required: true },
		Email: { type: String, unique: true, required: true, },
		Password: { type: String, required: true, minlength: 6, },
		Salt: { type: String },
		isRegular: { type: Boolean, default: false },
		lastLogin: { type: Date, default: null }
	},
	{ timestamps: true }
);

// Instance methods
UserAccountsSchema.methods = {
	authenticate: async function(plainText) {
		return await bcrypt.compare(plainText, this.Password);
	}
};

// Pre-save hook to hash password
UserAccountsSchema.pre('save', async function(next) {
	try {
		if (this.isModified('Password')) {
			const salt = await bcrypt.genSalt(10);
			this.Password = await bcrypt.hash(this.Password, salt);
			this.Salt = salt;
		}
		next();
	} catch (error) {
		console.error('Error in pre-save hook:', error);
		next(error);
	}
});

export default mongoose.models.UserAccounts || mongoose.model('UserAccounts', UserAccountsSchema);
