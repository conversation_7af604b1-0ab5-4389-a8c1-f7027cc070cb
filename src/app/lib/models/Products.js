import mongoose from "mongoose";

const statusEnums = ['In Stock', 'Out of Stock', 'Low Stock']
const ImageSchema = new mongoose.Schema({
	name: {
		type: String,
		required: true
	},
	contentType: {
		type: String,
		required: true
	},
	description: {
		type: String,
		required: false
	},
	url: {
		type: String,
		required: true
	}
});

export const ProductsSchema = new mongoose.Schema(
	{
		Name: { type: String, required: true, unique: true },
		Description: { type: String },
		MarketRatePrice: { type: Number, required: true },
		SellingPrice: { type: Number },
		QuantityType: { type: String },
		AvailableQuantity: { type: Number, required: true, default: 0 },
		Status: {
			type: String,
			required: true,
			default: 'In Stock',
			enum: { values: statusEnums, message: "Status must be one of: " + statusEnums.join(", ") },
		},
		StockLimitAlert: { type: Number, default: 10 },
		Images: { type: [ImageSchema], required: true },
		Slug: { type: String, required: true, unique: true },
		// Child Relationship
		createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Admins' },
		updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'Admins' },
		Categories: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'ProductCategories'
		},
		OrdersCount: { type: Number, default: 0 },
	}, { timestamps: true }
);

// Pre-save hook to update status
ProductsSchema.pre('save', async function(next) {
	try {
		if (this.AvailableQuantity < this.StockLimitAlert) {
			this.Status = 'Low Stock';
		} else if (this.AvailableQuantity > this.StockLimitAlert) {
			this.Status = 'In Stock';
		} else if (this.AvailableQuantity <= 0) {
			this.Status = 'Out of Stock';
		}
		next();
	} catch (error) {
		console.error('Error in pre-save hook:', error);
		next(error);
	}
});

// Pre-update hook to update status
ProductsSchema.pre('findOneAndUpdate', async function(next) {
	try {
		if (this.AvailableQuantity < this.StockLimitAlert) {
			this.Status = 'Low Stock';
		} else if (this.AvailableQuantity > this.StockLimitAlert) {
			this.Status = 'In Stock';
		} else if (this.AvailableQuantity <= 0) {
			this.Status = 'Out of Stock';
		}
		next();
	} catch (error) {
		console.error('Error in pre-save hook:', error);
		next(error);
	}
});

// Pre-remove middleware for single document
ProductsSchema.pre('deleteOne', { document: true }, async function(next) {
	try {

		// Update menu if needed
		if (this.Categories) {
			const ProductCategories = mongoose.model('ProductCategories');
			await ProductCategories.findByIdAndUpdate(this.Categories, {
				$pull: { Products: this._id }
			});
		}

		next();
	} catch (error) {
		next(error);
	}
});

// Pre-deleteMany middleware for multiple documents
ProductsSchema.pre('deleteMany', async function(next) {
	try {
		const Products = mongoose.model('Products');
		const products = await Products.find(this.getFilter());

		if (products.length > 0) {
			const categoryIds = products.map(product => product.Categories).filter(Boolean);

			if (categoryIds.length > 0) {
				const ProductCategories = mongoose.model('ProductCategories');
				await ProductCategories.updateMany(
					{ _id: { $in: categoryIds } },
					{ $pull: { Products: { $in: products.map(product => product._id) } } }
				);
			}
		}
		next();
	} catch (error) {
		next(error);
	}
});

export default mongoose.models.Products || mongoose.model("Products", ProductsSchema);
