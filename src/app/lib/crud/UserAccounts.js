import { Base<PERSON>rud } from "./BaseCrud";
import UserAccounts from "../models/UserAccounts";
import bcrypt from 'bcryptjs';

class UserAccountsCrud extends BaseCrud {
	constructor() {
		super(UserAccounts);
	}

	// Create a new user
	async RegisterUser({ first_name, last_name, email, password }) {
		try {
			const UserDetails = {
				FirstName: first_name,
				LastName: last_name,
				Email: email,
				Password: password
			};
			return await this.create(UserDetails);
		} catch (error) {
			return {
				returncode: 500,
				message: error.message,
				output: []
			};
		}
	}

	// Login User
	async LoginUser({ email, password }) {
		try {
			// Ensure database connection before operation
			await this.ensureConnection();

			const user = await this.model.findOne({ Email: email });
			if (!user) {
				return {
					returncode: 401,
					message: "Invalid credentials",
					output: []
				};
			}
			// Use bcrypt.compare to properly compare passwords
			const isValidPassword = await bcrypt.compare(password, user.Password);
			if (!isValidPassword) {
				console.log('Invalid password for user:', email);
				return {
					returncode: 401,
					message: "Invalid credentials",
					output: []
				};
			}
			// Don't send password in response
			const { Password, ...userData } = user.toObject();
			return {
				returncode: 200,
				message: "Login successful",
				output: [userData]
			};
		} catch (error) {
			return {
				returncode: 500,
				message: error.message,
				output: []
			};
		}
	}

	// Get user profile
	async getUserProfile({ user_id }) {
		try {
			// Ensure database connection before operation
			await this.ensureConnection();

			const user = await this.model.findById(user_id).select('-Password -Salt');
			if (!user) {
				return {
					returncode: 404,
					message: "User not found",
					output: []
				};
			}

			return {
				returncode: 200,
				message: "User profile retrieved successfully",
				output: [user]
			};
		} catch (error) {
			return {
				returncode: 500,
				message: error.message,
				output: []
			};
		}
	}

	// Update user profile
	async updateUserProfile({ user_id, first_name, last_name, email }) {
		try {
			// Ensure database connection before operation
			await this.ensureConnection();

			const updateData = {};
			if (first_name) updateData.FirstName = first_name;
			if (last_name) updateData.LastName = last_name;
			if (email) updateData.Email = email;

			// Check if email already exists (if email is being updated)
			if (email) {
				const existingUser = await this.model.findOne({
					Email: email,
					_id: { $ne: user_id }
				});
				if (existingUser) {
					return {
						returncode: 400,
						message: "Email already exists",
						output: []
					};
				}
			}

			const updatedUser = await this.model.findByIdAndUpdate(
				user_id,
				updateData,
				{ new: true, runValidators: true }
			).select('-Password -Salt');

			if (!updatedUser) {
				return {
					returncode: 404,
					message: "User not found",
					output: []
				};
			}

			return {
				returncode: 200,
				message: "User profile updated successfully",
				output: [updatedUser]
			};
		} catch (error) {
			return {
				returncode: 500,
				message: error.message,
				output: []
			};
		}
	}
}

const userAccountsCrud = new UserAccountsCrud();
export default userAccountsCrud;
