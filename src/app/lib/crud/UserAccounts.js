import { Base<PERSON>rud } from "./BaseCrud";
import UserAccounts from "../models/UserAccounts";
import bcrypt from 'bcryptjs';

class UserAccountsCrud extends BaseCrud {
	constructor() {
		super(UserAccounts);
	}

	// Create a new user
	async RegisterUser({ first_name, last_name, email, password }) {
		try {
			const UserDetails = {
				FirstName: first_name,
				LastName: last_name,
				Email: email,
				Password: password
			};
			return await this.create(UserDetails);
		} catch (error) {
			return {
				returncode: 500,
				message: error.message,
				output: []
			};
		}
	}

	// Login User
	async LoginUser({ email, password }) {
		try {
			// Ensure database connection before operation
			await this.ensureConnection();

			const user = await this.model.findOne({ Email: email });
			if (!user) {
				return {
					returncode: 401,
					message: "Invalid credentials",
					output: []
				};
			}
			// Use bcrypt.compare to properly compare passwords
			const isValidPassword = await bcrypt.compare(password, user.Password);
			if (!isValidPassword) {
				console.log('Invalid password for user:', email);
				return {
					returncode: 401,
					message: "Invalid credentials",
					output: []
				};
			}
			// Don't send password in response
			const { Password, ...userData } = user.toObject();
			return {
				returncode: 200,
				message: "Login successful",
				output: [userData]
			};
		} catch (error) {
			return {
				returncode: 500,
				message: error.message,
				output: []
			};
		}
	}
}

const userAccountsCrud = new UserAccountsCrud();
export default userAccountsCrud;
