import { Base<PERSON>rud } from "./BaseCrud";
import Slides from "../models/Slides";
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

class SlidesCrud extends BaseCrud {
	constructor() {
		super(Slides);
	}

	async ImageCreation(image, slug) {
		try {
			const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'slides', slug);
			await mkdir(uploadDir, { recursive: true });

			// Create unique filename
			const randomString = crypto.randomUUID();
			const extension = image.name.split('.').pop() || 'jpg';
			const fileName = `${randomString}.${extension}`;
			const filePath = path.join(uploadDir, fileName);

			// Extract base64 data
			const base64Data = image.base64Data.split(';base64,').pop();
			if (base64Data) {
				// Save the file
				const buffer = Buffer.from(base64Data, 'base64');
				await writeFile(filePath, buffer);

				// Create image data object
				const imageData = {
					name: fileName,
					contentType: image.contentType,
					description: image.description || '',
					url: `/uploads/slides/${slug}/${fileName}`
				};
				return imageData;

			} else {
				return { returncode: 400, message: 'Image not provided', output: [] };
			}

		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}

	}

	async createSlide({ slide_name, imageData, type, slug, admin_id }) {
		try {
			const data = {
				Name: slide_name,
				Image: imageData,
				Slug: slug,
				Type: type,
				createdBy: admin_id
			}
			return await this.create(data);
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async slideExists({ slide_name }) {
		try {
			const result = await this.exists({ Name: slide_name });
			if (result.output === true) {
				return { returncode: 200, message: 'Slide Exists', output: [] }
			} else {
				return { returncode: 400, message: 'No Slide with this name Exists', output: [] }
			}
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async slideExistsById({ id }) {
		try {
			return await this.readOne({ _id: id });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async getAllSlides() {
		try {
			return await this.readMany({}, { populate: [{ path: 'createdBy' }, { path: 'updatedBy' }] });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async deleteSlide({ id }) {
		try {
			return await this.delete({ _id: id });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}
}

const slidesCrud = new SlidesCrud();
export default slidesCrud;
