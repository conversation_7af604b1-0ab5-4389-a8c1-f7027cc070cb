import Orders from "../models/Orders";
import { BaseCrud } from "./BaseCrud";

class Orders<PERSON>rud extends BaseCrud {
	constructor() {
		super(Orders);
	}

	async createOrder({
		name, contact, user_id, apartment, street_address, landmark, city, pin_code, order_status = 'Placed', payment_status = 'paid', payment_mode, cart,
	}) {
		try {
			return await this.create({
				Name: name,
				Contact: contact,
				User: user_id,
				Apartment: apartment,
				StreetAddress: street_address,
				Landmark: landmark,
				Pincode: pin_code,
				City: city,
				OrderStatus: order_status || 'Order Placed',
				PaymentStatus: payment_status || 'Paid',
				PaymentMode: payment_mode,
				Cart: cart
			});
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async fetchAllOrders() {
		try {
			return await this.readMany({}, {
				populate: [{
					path: 'Cart.ProductId',
					populate: [{ path: 'Categories', }],
				}]
			});
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async fetchOrdersofUser({ user_id }) {
		try {
			const exists = await this.exists({ User: user_id });
			if (exists.output) {
				return await this.readMany({ User: user_id }, {
					populate: [{
						path: 'Cart.ProductId',
						populate: [{ path: 'Categories', }],
					}]
				});
			}
			return { returncode: 400, message: 'Start purchase before, for order to display...', output: [] };
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async fetchOrderInfo({ user_id, order_id }) {
		try {
			const exists = await this.exists({ User: user_id, _id: order_id });
			if (exists.output) {
				return await this.readOne({ User: user_id, _id: order_id }, {
					populate: [{
						path: 'Cart.ProductId',
						populate: [{ path: 'Categories', }],
					}]
				});
			}
			return { returncode: 400, message: 'No info found on this order to display...', output: [] };
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async updateOrderStatus({ order_id, order_status }) {
		try {
			return await this.update({ _id: order_id }, { OrderStatus: order_status });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async updatePaymentStatus({ order_id, payment_status }) {
		try {
			return await this.update({ _id: order_id }, { PaymentStatus: payment_status });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async updatePaymentMode({ order_id, payment_mode }) {
		try {
			return await this.update({ _id: order_id }, { PaymentMode: payment_mode });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async deleteOrder({ order_id }) {
		try {
			return await this.readMany({ _id: order_id });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

}

const ordersCrud = new OrdersCrud();
export default ordersCrud
