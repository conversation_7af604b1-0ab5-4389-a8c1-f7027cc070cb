import { BaseCrud } from "./BaseCrud";
import Products from "../models/Products";
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

class ProductsCrud extends BaseCrud {
	constructor() {
		super(Products);
	}

	async ImageCreation(image, slug) {
		try {
			const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'products', slug);
			await mkdir(uploadDir, { recursive: true });

			// Create unique filename
			const randomString = crypto.randomUUID();
			const extension = image.name.split('.').pop() || 'jpg';
			const fileName = `${randomString}.${extension}`;
			const filePath = path.join(uploadDir, fileName);

			// Extract base64 data
			const base64Data = image.base64Data.split(';base64,').pop();
			if (base64Data) {
				// Save the file
				const buffer = Buffer.from(base64Data, 'base64');
				await writeFile(filePath, buffer);

				// Create image data object
				const imageData = {
					name: fileName,
					contentType: image.contentType,
					description: image.description || '',
					url: `/uploads/products/${slug}/${fileName}`
				};
				return imageData;

			} else {
				return { returncode: 400, message: 'Image not provided', output: [] };
			}

		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}

	}

	async addProduct({ product_name, description, imageData, category_id, mrp, selling_price, quantity_type, available_quantity, slug, limit, admin_id }) {
		try {
			const data = {
				Name: product_name,
				Description: description,
				Images: imageData,
				Categories: category_id,
				MarketRatePrice: mrp,
				SellingPrice: selling_price,
				QuantityType: quantity_type,
				AvailableQuantity: available_quantity,
				Slug: slug,
				StockLimitAlert: limit,
				createdBy: admin_id
			}
			return await this.create(data);
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async productExists({ product_name }) {
		try {
			const result = await this.exists({ Name: product_name });
			if (result.output === true) {
				return { returncode: 200, message: 'Product Exists', output: [] }
			} else {
				return { returncode: 400, message: 'No Product with this name Exists', output: [] }
			}
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async productExistsById({ id }) {
		try {
			return await this.readOne({ _id: id });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async getAllProducts() {
		try {
			return await this.readMany({}, { populate: [{ path: 'createdBy' }, { path: 'Categories' }] });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async getProductsByCategory({ category_id }) {
		try {
			console.log(category_id)
			return await this.readMany({
				'Categories': category_id
			}, { populate: [{ path: 'createdBy' }, { path: 'Categories' }] });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async updateInfo({ product_id, product_name, description, mrp, selling_price, quantity_type, available_quantity, limit, admin_id }) {
		try {
			return await this.update({ _id: product_id }, {
				Name: product_name,
				Description: description,
				MarketRatePrice: mrp,
				SellingPrice: selling_price,
				QuantityType: quantity_type,
				AvailableQuantity: available_quantity,
				StockLimitAlert: limit,
				updatedBy: admin_id,
			});
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async deleteProduct({ id }) {
		try {
			return await this.delete({ _id: id });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

}

const productsCrud = new ProductsCrud();
export default productsCrud;
