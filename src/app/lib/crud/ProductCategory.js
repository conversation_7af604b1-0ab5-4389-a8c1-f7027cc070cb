import { BaseCrud } from "./BaseCrud";
import ProductCategories from "../models/ProductCategories";
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

class ProductCategoriesCrud extends BaseCrud {
	constructor() {
		super(ProductCategories)
	}

	async ImageCreation(image, slug) {
		try {
			const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'categories', slug);
			await mkdir(uploadDir, { recursive: true });

			// Create unique filename
			const randomString = crypto.randomUUID();
			const extension = image.name.split('.').pop() || 'jpg';
			const fileName = `${randomString}.${extension}`;
			const filePath = path.join(uploadDir, fileName);

			// Extract base64 data
			const base64Data = image.base64Data.split(';base64,').pop();
			if (base64Data) {
				// Save the file
				const buffer = Buffer.from(base64Data, 'base64');
				await writeFile(filePath, buffer);

				// Create image data object
				const imageData = {
					name: fileName,
					contentType: image.contentType,
					description: image.description || '',
					url: `/uploads/categories/${slug}/${fileName}`
				};
				return imageData;

			} else {
				return { returncode: 400, message: 'Image not provided', output: [] };
			}

		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}

	}

	async createCategory({ category_name, description, imageData, color, slug, admin_id }) {
		try {
			const data = {
				Name: category_name,
				Description: description,
				Icon: imageData,
				Color: color,
				Slug: slug,
				createdBy: admin_id
			}
			return await this.create(data);
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async categoryExists({ category_name }) {
		try {
			const result = await this.exists({ Name: category_name });
			if (result.output === true) {
				return { returncode: 200, message: 'Category Exists', output: [] }
			} else {
				return { returncode: 400, message: 'No Category with this name Exists', output: [] }
			}
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async categoryExistsById({ id }) {
		try {
			return await this.readOne({ _id: id });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async getAllCategories() {
		try {
			return await this.readMany({}, { populate: [{ path: 'createdBy' }] });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async deleteByCategory({ id }) {
		try {
			return await this.delete({ _id: id });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}
}

const productCategoriesCrud = new ProductCategoriesCrud();
export default productCategoriesCrud;
