import { <PERSON><PERSON>rud } from "./BaseCrud";
import Admins from "../models/Admins";
import bcrypt from 'bcryptjs';

class AdminsCrud extends BaseCrud {
	constructor() {
		super(Admins);
	}

	// Create a new admin
	async AddNewUser({ first_name, last_name, email, password }) {
		try {
			const UserDetails = {
				FirstName: first_name,
				LastName: last_name,
				Email: email,
				Password: password
			};
			return await this.create(UserDetails);
		} catch (error) {
			return {
				returncode: 500,
				message: error.message,
				output: []
			};
		}
	}

	// Login Admin
	async Login({ email, password }) {
		try {
			// Ensure database connection before operation
			await this.ensureConnection();

			const user = await this.model.findOne({ Email: email });
			if (!user) {
				return {
					returncode: 401,
					message: "Invalid credentials",
					output: []
				};
			}
			// Use bcrypt.compare to properly compare passwords
			const isValidPassword = await bcrypt.compare(password, user.Password);
			if (!isValidPassword) {
				console.log('Invalid password for user:', email);
				return {
					returncode: 401,
					message: "Invalid credentials",
					output: []
				};
			}
			// Don't send password in response
			const { Password, ...userData } = user.toObject();
			return {
				returncode: 200,
				message: "Login successful",
				output: [userData]
			};
		} catch (error) {
			return {
				returncode: 500,
				message: error.message,
				output: []
			};
		}
	}

	async ReadUsers() {
		try {
			return await this.readMany({});
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async updatePassword({ email, password }) {
		try {
			return await this.update({ Email: email }, { Password: password });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

	async deleteUser({ id }) {
		try {
			return await this.delete({ _id: id });
		} catch (error) {
			return { returncode: 500, message: error.message, output: [] };
		}
	}

}

const adminsCrud = new AdminsCrud();
export default adminsCrud;
