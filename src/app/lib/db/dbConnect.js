import mongoose from 'mongoose'

// Get MongoDB URI from environment variables with fallback
const MONGODB_URI = process.env.MONGODB_URI ||
	process.env.NEXT_PUBLIC_MONGODB_URI ||
	'mongodb+srv://admin123:<EMAIL>/?retryWrites=true&w=majority&appName=Grocero';

if (!MONGODB_URI) {
	throw new Error(
		'Please define the MONGODB_URI environment variable inside .env.local or in Vercel environment variables',
	)
}

let cached = global.mongoose

if (!cached) {
	cached = global.mongoose = { conn: null, promise: null }
}

async function dbConnect() {
	// If we already have a connection, return it
	if (cached.conn) {
		return cached.conn
	}

	// If we don't have a promise, create one
	if (!cached.promise) {
		const opts = {
			bufferCommands: false,
			// Additional options for better Vercel compatibility
			maxPoolSize: 10, // Maintain up to 10 socket connections
			serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
			socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
			family: 4 // Use IPv4, skip trying IPv6
		}

		console.log('🔄 Connecting to MongoDB...')
		cached.promise = mongoose.connect(MONGODB_URI, opts).then(mongoose => {
			console.log('✅ MongoDB connected successfully')
			return mongoose
		}).catch(error => {
			console.error('❌ MongoDB connection error:', error)
			throw error
		})
	}

	try {
		cached.conn = await cached.promise
	} catch (e) {
		cached.promise = null
		console.error('❌ Failed to establish database connection:', e)
		throw e
	}

	return cached.conn
}

export default dbConnect
