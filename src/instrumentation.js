import mongoose from "mongoose";
import dbConnect from "./app/lib/db/dbConnect";
import { UserAccountsSchema } from "./app/lib/models/UserAccounts";
import { ProductCategoriesSchema } from "./app/lib/models/ProductCategories";
import { AdminsSchema } from "./app/lib/models/Admins";
import { ProductsSchema } from "./app/lib/models/Products";
import { SlidesSchema } from "./app/lib/models/Slides";
import { OrdersSchema } from "./app/lib/models/Orders";

// Entrypoint
export async function register() {
	// Only run in production or when explicitly enabled
	if (process.env.NODE_ENV === 'production' || process.env.ENABLE_INSTRUMENTATION === 'true') {
		try {
			console.log("🚀 Starting database initialization...");
			await dbConnect();
			await initializeModels();
			console.log("✅ Database connected and models initialized successfully");
		} catch (error) {
			console.log("❌ Error initializing database:", error);
		}
	} else {
		console.log("⏭️ Skipping database initialization in development mode");
	}
}

async function initializeModels() {
	const modelDefinition = [
		{ name: 'UserAccounts', schema: UserAccountsSchema },
		{ name: 'ProductCategories', schema: ProductCategoriesSchema },
		{ name: 'Admins', schema: AdminsSchema },
		{ name: 'Products', schema: ProductsSchema },
		{ name: 'Slides', schema: SlidesSchema },
		{ name: 'Orders', schema: OrdersSchema },
	];

	for (const { name, schema } of modelDefinition) {
		try {
			// Check if model exists, if not create it
			mongoose.models[name] || mongoose.model(name, schema);

			// Test Users
			switch (name) {
				case 'UserAccounts':
					const UserAccounts = mongoose.model(name);
					const userExists = await UserAccounts.exists({});
					if (!userExists) {
						await UserAccounts.create({
							FirstName: 'Test',
							LastName: 'User',
							Email: '<EMAIL>',
							Password: 'test123', // Will be hashed by pre-save hook
						});
						console.log(`✅ Created default User: <EMAIL>`);
					}
					break;
				case 'Admins':
					const Admins = mongoose.model(name);
					const adminExists = await Admins.exists({});
					if (!adminExists) {
						await Admins.create({
							FirstName: 'Root',
							LastName: 'Admin',
							Email: '<EMAIL>',
							Password: 'admin123', // Will be hashed by pre-save hook
						});
						console.log(`✅ Created default Admin: <EMAIL>`);
					}
					break;

				default:
					break;
			}

			console.log(`✅ Model initialized: ${name}`);
		} catch (error) {
			console.error(`❌ Error initializing model ${name}:`, error);
		}
	}
} 
