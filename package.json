{"name": "grocery-ecommerce", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test-db": "node test-db-connection.js"}, "dependencies": {"@hookform/resolvers": "^4.0.0", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@tabler/icons-react": "^3.30.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.5.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.474.0", "mongoose": "^8.10.0", "next": "15.1.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "slugify": "^1.6.6", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "22.13.1", "@types/react": "19.0.8", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "5.7.3"}}